{"tests/backend/test_api_endpoints.py::TestAPIEndpoints::test_signup_endpoint_exists": true, "tests/backend/test_api_endpoints.py::TestAPIEndpoints::test_availability_endpoint_exists": true, "tests/backend/test_api_endpoints.py::TestAPIEndpoints::test_availability_endpoint_validation": true, "tests/backend/test_api_endpoints.py::TestAPIEndpoints::test_unsupported_methods": true, "tests/backend/test_api_endpoints.py::TestAPIEndpoints::test_home_page_loads": true, "tests/backend/test_api_endpoints.py::TestAPIEndpoints::test_signup_page_loads": true, "tests/backend/test_api_endpoints.py::TestAPIEndpoints::test_admin_dashboard_loads": true, "tests/backend/test_tenant_signup.py::TestTenantSignup::test_valid_tenant_signup_validation": true, "tests/backend/test_tenant_signup.py::TestTenantSignup::test_invalid_tenant_signup": true, "tests/backend/test_tenant_signup.py::TestTenantSignup::test_availability_check_endpoint": true, "tests/backend/test_tenant_signup.py::TestTenantSignup::test_password_validation": true, "tests/backend/test_tenant_signup.py::TestTenantSignup::test_missing_required_fields": true, "tests/backend/test_tenant_signup.py::TestTenantSignup::test_email_format_validation": true, "tests/backend/test_live_api.py::TestLiveAPI::test_home_page_loads": true, "tests/backend/test_live_api.py::TestLiveAPI::test_signup_page_loads": true, "tests/backend/test_live_api.py::TestLiveAPI::test_admin_dashboard_loads": true, "tests/backend/test_live_api.py::TestLiveAPI::test_availability_check_endpoint": true, "tests/backend/test_live_api.py::TestLiveAPI::test_availability_endpoint_validation": true, "tests/backend/test_live_api.py::TestLiveAPI::test_signup_endpoint_validation": true, "tests/backend/test_live_api.py::TestLiveAPI::test_signup_password_validation": true, "tests/backend/test_live_api.py::TestLiveAPI::test_signup_with_valid_data_structure": true, "tests/backend/test_live_api.py::TestLiveAPI::test_unsupported_methods": true}