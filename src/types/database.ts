/**
 * Supabase Database Type Definitions for BHEEMDINE B2B SaaS
 * Auto-generated types for type-safe database operations
 */

export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      Tenant: {
        Row: {
          id: string
          name: string
          slug: string
          email: string
          phone: string | null
          address: string | null
          city: string | null
          state: string | null
          country: string
          zipCode: string | null
          logoUrl: string | null
          primaryColor: string | null
          description: string | null
          website: string | null
          status: 'ACTIVE' | 'SUSPENDED' | 'INACTIVE' | 'PENDING'
          planType: 'TRIAL' | 'BASIC' | 'PREMIUM' | 'ENTERPRISE'
          planExpiry: string | null
          settings: Json | null
          features: Json
          isActive: boolean
          isDeleted: boolean
          deletedAt: string | null
          createdAt: string
          updatedAt: string
        }
        Insert: {
          id?: string
          name: string
          slug: string
          email: string
          phone?: string | null
          address?: string | null
          city?: string | null
          state?: string | null
          country?: string
          zipCode?: string | null
          logoUrl?: string | null
          primaryColor?: string | null
          description?: string | null
          website?: string | null
          status?: 'ACTIVE' | 'SUSPENDED' | 'INACTIVE' | 'PENDING'
          planType?: 'TRIAL' | 'BASIC' | 'PREMIUM' | 'ENTERPRISE'
          planExpiry?: string | null
          settings?: Json | null
          features?: Json
          isActive?: boolean
          isDeleted?: boolean
          deletedAt?: string | null
          createdAt?: string
          updatedAt?: string
        }
        Update: {
          id?: string
          name?: string
          slug?: string
          email?: string
          phone?: string | null
          address?: string | null
          city?: string | null
          state?: string | null
          country?: string
          zipCode?: string | null
          logoUrl?: string | null
          primaryColor?: string | null
          description?: string | null
          website?: string | null
          status?: 'ACTIVE' | 'SUSPENDED' | 'INACTIVE' | 'PENDING'
          planType?: 'TRIAL' | 'BASIC' | 'PREMIUM' | 'ENTERPRISE'
          planExpiry?: string | null
          settings?: Json | null
          features?: Json
          isActive?: boolean
          isDeleted?: boolean
          deletedAt?: string | null
          createdAt?: string
          updatedAt?: string
        }
        Relationships: []
      }
      AdminUser: {
        Row: {
          id: string
          tenantId: string
          authUserId: string
          email: string
          firstName: string | null
          lastName: string | null
          phone: string | null
          avatar: string | null
          role: 'OWNER' | 'ADMIN' | 'MANAGER' | 'STAFF' | 'READONLY'
          status: 'ACTIVE' | 'INACTIVE' | 'PENDING' | 'SUSPENDED'
          permissions: Json
          preferences: Json
          timezone: string
          language: string
          lastLoginAt: string | null
          isOnline: boolean
          createdAt: string
          updatedAt: string
        }
        Insert: {
          id?: string
          tenantId: string
          authUserId: string
          email: string
          firstName?: string | null
          lastName?: string | null
          phone?: string | null
          avatar?: string | null
          role?: 'OWNER' | 'ADMIN' | 'MANAGER' | 'STAFF' | 'READONLY'
          status?: 'ACTIVE' | 'INACTIVE' | 'PENDING' | 'SUSPENDED'
          permissions?: Json
          preferences?: Json
          timezone?: string
          language?: string
          lastLoginAt?: string | null
          isOnline?: boolean
          createdAt?: string
          updatedAt?: string
        }
        Update: {
          id?: string
          tenantId?: string
          authUserId?: string
          email?: string
          firstName?: string | null
          lastName?: string | null
          phone?: string | null
          avatar?: string | null
          role?: 'OWNER' | 'ADMIN' | 'MANAGER' | 'STAFF' | 'READONLY'
          status?: 'ACTIVE' | 'INACTIVE' | 'PENDING' | 'SUSPENDED'
          permissions?: Json
          preferences?: Json
          timezone?: string
          language?: string
          lastLoginAt?: string | null
          isOnline?: boolean
          createdAt?: string
          updatedAt?: string
        }
        Relationships: [
          {
            foreignKeyName: "AdminUser_tenantId_fkey"
            columns: ["tenantId"]
            isOneToOne: false
            referencedRelation: "Tenant"
            referencedColumns: ["id"]
          }
        ]
      }
      MenuItem: {
        Row: {
          id: string
          tenantId: string
          name: string
          description: string | null
          category: string
          price: number
          imageUrl: string | null
          isAvailable: boolean
          isVegetarian: boolean
          isVegan: boolean
          preparationTime: number
          sortOrder: number
          createdAt: string
          updatedAt: string
        }
        Insert: {
          id?: string
          tenantId: string
          name: string
          description?: string | null
          category: string
          price: number
          imageUrl?: string | null
          isAvailable?: boolean
          isVegetarian?: boolean
          isVegan?: boolean
          preparationTime?: number
          sortOrder?: number
          createdAt?: string
          updatedAt?: string
        }
        Update: {
          id?: string
          tenantId?: string
          name?: string
          description?: string | null
          category?: string
          price?: number
          imageUrl?: string | null
          isAvailable?: boolean
          isVegetarian?: boolean
          isVegan?: boolean
          preparationTime?: number
          sortOrder?: number
          createdAt?: string
          updatedAt?: string
        }
        Relationships: [
          {
            foreignKeyName: "MenuItem_tenantId_fkey"
            columns: ["tenantId"]
            referencedRelation: "Tenant"
            referencedColumns: ["id"]
          }
        ]
      }
      Room: {
        Row: {
          id: string
          tenantId: string
          roomNumber: string
          floor: string | null
          qrCode: string
          capacity: number
          status: 'AVAILABLE' | 'OCCUPIED' | 'RESERVED' | 'MAINTENANCE'
          createdAt: string
          updatedAt: string
        }
        Insert: {
          id?: string
          tenantId: string
          roomNumber: string
          floor?: string | null
          qrCode: string
          capacity?: number
          status?: 'AVAILABLE' | 'OCCUPIED' | 'RESERVED' | 'MAINTENANCE'
          createdAt?: string
          updatedAt?: string
        }
        Update: {
          id?: string
          tenantId?: string
          roomNumber?: string
          floor?: string | null
          qrCode?: string
          capacity?: number
          status?: 'AVAILABLE' | 'OCCUPIED' | 'RESERVED' | 'MAINTENANCE'
          createdAt?: string
          updatedAt?: string
        }
        Relationships: [
          {
            foreignKeyName: "Room_tenantId_fkey"
            columns: ["tenantId"]
            referencedRelation: "Tenant"
            referencedColumns: ["id"]
          }
        ]
      }
      Order: {
        Row: {
          id: string
          tenantId: string
          userId: string
          roomId: string
          orderNumber: string
          status: 'PENDING' | 'CONFIRMED' | 'PREPARING' | 'READY' | 'DELIVERED' | 'COMPLETED' | 'CANCELLED'
          totalAmount: number
          notes: string | null
          assignedToId: string | null
          placedAt: string
          confirmedAt: string | null
          completedAt: string | null
          deliveredAt: string | null
          cancelledAt: string | null
        }
        Insert: {
          id?: string
          tenantId: string
          userId: string
          roomId: string
          orderNumber: string
          status?: 'PENDING' | 'CONFIRMED' | 'PREPARING' | 'READY' | 'DELIVERED' | 'COMPLETED' | 'CANCELLED'
          totalAmount: number
          notes?: string | null
          assignedToId?: string | null
          placedAt?: string
          confirmedAt?: string | null
          completedAt?: string | null
          deliveredAt?: string | null
          cancelledAt?: string | null
        }
        Update: {
          id?: string
          tenantId?: string
          userId?: string
          roomId?: string
          orderNumber?: string
          status?: 'PENDING' | 'CONFIRMED' | 'PREPARING' | 'READY' | 'DELIVERED' | 'COMPLETED' | 'CANCELLED'
          totalAmount?: number
          notes?: string | null
          assignedToId?: string | null
          placedAt?: string
          confirmedAt?: string | null
          completedAt?: string | null
          deliveredAt?: string | null
          cancelledAt?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "Order_tenantId_fkey"
            columns: ["tenantId"]
            referencedRelation: "Tenant"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "Order_roomId_fkey"
            columns: ["roomId"]
            referencedRelation: "Room"
            referencedColumns: ["id"]
          }
        ]
      }
      AuditLog: {
        Row: {
          id: string
          tenantId: string
          userId: string | null
          action: string
          resource: string
          resourceId: string | null
          ipAddress: string | null
          userAgent: string | null
          metadata: Json | null
          createdAt: string
        }
        Insert: {
          id?: string
          tenantId: string
          userId?: string | null
          action: string
          resource: string
          resourceId?: string | null
          ipAddress?: string | null
          userAgent?: string | null
          metadata?: Json | null
          createdAt?: string
        }
        Update: {
          id?: string
          tenantId?: string
          userId?: string | null
          action?: string
          resource?: string
          resourceId?: string | null
          ipAddress?: string | null
          userAgent?: string | null
          metadata?: Json | null
          createdAt?: string
        }
        Relationships: [
          {
            foreignKeyName: "AuditLog_tenantId_fkey"
            columns: ["tenantId"]
            referencedRelation: "Tenant"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "AuditLog_userId_fkey"
            columns: ["userId"]
            referencedRelation: "AdminUser"
            referencedColumns: ["id"]
          }
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      validate_admin_tenant_access: {
        Args: {
          p_email: string
          p_tenant_slug: string
        }
        Returns: boolean
      }
      switch_admin_tenant_context: {
        Args: {
          p_user_id: string
          p_tenant_slug: string
        }
        Returns: boolean
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}