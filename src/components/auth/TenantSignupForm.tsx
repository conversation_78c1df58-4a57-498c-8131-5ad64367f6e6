/**
 * B2B Tenant Signup Form with Enhanced Security and Google OAuth
 * Designed for restaurant and hotel business owners
 */

'use client'

import React, { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { 
  Building2, 
  Mail, 
  Lock, 
  User, 
  Phone, 
  MapPin, 
  Globe, 
  CheckCircle, 
  AlertCircle,
  Eye,
  EyeOff,
  Chrome
} from 'lucide-react'
import { supabase } from '@/utils/supabase/client'

interface TenantSignupData {
  // Business Information
  businessName: string
  tenantSlug: string
  businessType: 'restaurant' | 'hotel' | 'cafe' | 'bar' | 'other'
  businessDescription: string
  
  // Contact Information
  businessEmail: string
  businessPhone: string
  businessWebsite: string
  
  // Address Information
  businessAddress: string
  businessCity: string
  businessState: string
  businessCountry: string
  businessZip: string
  
  // Admin User Information
  adminName: string
  adminEmail: string
  adminPhone: string
  adminPassword: string
  confirmPassword: string
  
  // Settings
  timezone: string
  currency: string
  language: string
  
  // Agreements
  agreeToTerms: boolean
  agreeToPrivacy: boolean
  allowMarketing: boolean
}

interface ValidationErrors {
  [key: string]: string
}

interface SignupStep {
  id: number
  title: string
  description: string
  fields: (keyof TenantSignupData)[]
}

const SIGNUP_STEPS: SignupStep[] = [
  {
    id: 1,
    title: "Business Information",
    description: "Tell us about your restaurant or hotel",
    fields: ['businessName', 'tenantSlug', 'businessType', 'businessDescription']
  },
  {
    id: 2,
    title: "Contact Details",
    description: "How can we reach your business?",
    fields: ['businessEmail', 'businessPhone', 'businessWebsite', 'businessAddress', 'businessCity', 'businessState', 'businessCountry', 'businessZip']
  },
  {
    id: 3,
    title: "Admin Account",
    description: "Create your administrator account",
    fields: ['adminName', 'adminEmail', 'adminPhone', 'adminPassword', 'confirmPassword']
  },
  {
    id: 4,
    title: "Preferences",
    description: "Configure your business settings",
    fields: ['timezone', 'currency', 'language', 'agreeToTerms', 'agreeToPrivacy', 'allowMarketing']
  }
]

const BUSINESS_TYPES = [
  { value: 'restaurant', label: 'Restaurant', icon: '🍽️' },
  { value: 'hotel', label: 'Hotel', icon: '🏨' },
  { value: 'cafe', label: 'Café', icon: '☕' },
  { value: 'bar', label: 'Bar/Pub', icon: '🍺' },
  { value: 'other', label: 'Other', icon: '🏢' }
]

export default function TenantSignupForm() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [currentStep, setCurrentStep] = useState(1)
  const [isLoading, setIsLoading] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [slugAvailable, setSlugAvailable] = useState<boolean | null>(null)
  const [slugSuggestions, setSlugSuggestions] = useState<string[]>([])
  const [errors, setErrors] = useState<ValidationErrors>({})
  const [submitAttempted, setSubmitAttempted] = useState(false)

  const [formData, setFormData] = useState<TenantSignupData>({
    // Business Information
    businessName: '',
    tenantSlug: '',
    businessType: 'restaurant',
    businessDescription: '',
    
    // Contact Information
    businessEmail: '',
    businessPhone: '',
    businessWebsite: '',
    
    // Address Information
    businessAddress: '',
    businessCity: '',
    businessState: '',
    businessCountry: 'US',
    businessZip: '',
    
    // Admin User Information
    adminName: '',
    adminEmail: '',
    adminPhone: '',
    adminPassword: '',
    confirmPassword: '',
    
    // Settings
    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    currency: 'USD',
    language: 'en',
    
    // Agreements
    agreeToTerms: false,
    agreeToPrivacy: false,
    allowMarketing: false
  })

  // Auto-generate tenant slug from business name
  useEffect(() => {
    if (formData.businessName && !formData.tenantSlug) {
      const slug = formData.businessName
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .substring(0, 50)
      
      setFormData(prev => ({ ...prev, tenantSlug: slug }))
    }
  }, [formData.businessName])

  // Check slug availability
  useEffect(() => {
    const checkSlugAvailability = async () => {
      if (formData.tenantSlug.length >= 3) {
        try {
          const { data, error } = await supabase
            .from('Tenant')
            .select('slug')
            .eq('slug', formData.tenantSlug)
            .single()

          setSlugAvailable(!data)
          
          if (data) {
            // Generate suggestions
            const baseName = formData.businessName.toLowerCase().replace(/[^a-z0-9]/g, '')
            const suggestions = []
            for (let i = 1; i <= 3; i++) {
              suggestions.push(`${baseName}${i}`)
              suggestions.push(`${formData.tenantSlug}${i}`)
            }
            setSlugSuggestions(suggestions.slice(0, 3))
          } else {
            setSlugSuggestions([])
          }
        } catch (error) {
          console.error('Error checking slug availability:', error)
        }
      }
    }

    const timeoutId = setTimeout(checkSlugAvailability, 500)
    return () => clearTimeout(timeoutId)
  }, [formData.tenantSlug, formData.businessName])

  const validateStep = (step: number): boolean => {
    const stepConfig = SIGNUP_STEPS.find(s => s.id === step)
    if (!stepConfig) return false

    const stepErrors: ValidationErrors = {}

    stepConfig.fields.forEach(field => {
      const value = formData[field]

      switch (field) {
        case 'businessName':
          if (!value || (value as string).trim().length < 2) {
            stepErrors[field] = 'Business name must be at least 2 characters'
          }
          break

        case 'tenantSlug':
          if (!value || (value as string).length < 3) {
            stepErrors[field] = 'Tenant slug must be at least 3 characters'
          } else if (!/^[a-z0-9-]+$/.test(value as string)) {
            stepErrors[field] = 'Only lowercase letters, numbers, and hyphens allowed'
          } else if (slugAvailable === false) {
            stepErrors[field] = 'This slug is already taken'
          }
          break

        case 'businessEmail':
        case 'adminEmail':
          if (!value || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value as string)) {
            stepErrors[field] = 'Please enter a valid email address'
          }
          break

        case 'businessPhone':
        case 'adminPhone':
          if (!value || !/^\+?[\d\s-()]+$/.test(value as string)) {
            stepErrors[field] = 'Please enter a valid phone number'
          }
          break

        case 'adminPassword':
          const password = value as string
          if (!password || password.length < 12) {
            stepErrors[field] = 'Password must be at least 12 characters'
          } else if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])/.test(password)) {
            stepErrors[field] = 'Password must include uppercase, lowercase, number, and special character'
          }
          break

        case 'confirmPassword':
          if (value !== formData.adminPassword) {
            stepErrors[field] = 'Passwords do not match'
          }
          break

        case 'agreeToTerms':
        case 'agreeToPrivacy':
          if (!value) {
            stepErrors[field] = 'You must agree to continue'
          }
          break

        default:
          if (!value && typeof value !== 'boolean') {
            stepErrors[field] = 'This field is required'
          }
      }
    })

    setErrors(prev => ({ ...prev, ...stepErrors }))
    return Object.keys(stepErrors).length === 0
  }

  const handleInputChange = (field: keyof TenantSignupData, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    
    // Clear error for this field
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  const handleNextStep = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, SIGNUP_STEPS.length))
    }
  }

  const handlePrevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1))
  }

  const handleGoogleSignup = async () => {
    try {
      setIsLoading(true)
      
      const { error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: `${window.location.origin}/auth/callback?signup=true&tenant=${formData.tenantSlug}`,
          queryParams: {
            signup: 'true',
            business_name: formData.businessName,
            tenant_slug: formData.tenantSlug,
            business_type: formData.businessType
          }
        }
      })

      if (error) throw error
    } catch (error) {
      console.error('Google signup error:', error)
      setErrors({ general: 'Google signup failed. Please try again.' })
    } finally {
      setIsLoading(false)
    }
  }

  const handleSubmit = async () => {
    setSubmitAttempted(true)
    
    if (!validateStep(4)) {
      return
    }

    try {
      setIsLoading(true)

      // Create tenant signup request
      const signupRequest = {
        // Business information
        business_name: formData.businessName,
        tenant_slug: formData.tenantSlug,
        business_type: formData.businessType,
        business_description: formData.businessDescription,
        business_email: formData.businessEmail,
        business_phone: formData.businessPhone,
        business_website: formData.businessWebsite,
        business_address: formData.businessAddress,
        business_city: formData.businessCity,
        business_state: formData.businessState,
        business_country: formData.businessCountry,
        business_zip: formData.businessZip,
        
        // Admin user information
        admin_name: formData.adminName,
        admin_email: formData.adminEmail,
        admin_phone: formData.adminPhone,
        admin_password: formData.adminPassword,
        
        // Settings
        timezone: formData.timezone,
        currency: formData.currency,
        language: formData.language,
        
        // Marketing preferences
        allow_marketing: formData.allowMarketing
      }

      // Sign up the admin user with Supabase Auth
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email: formData.adminEmail,
        password: formData.adminPassword,
        options: {
          data: {
            name: formData.adminName,
            tenant_slug: formData.tenantSlug,
            business_name: formData.businessName,
            role: 'tenant_admin'
          }
        }
      })

      if (authError) throw authError

      if (authData.user && !authData.session) {
        // Email confirmation required
        router.push(`/auth/verify-email?email=${encodeURIComponent(formData.adminEmail)}&tenant=${formData.tenantSlug}`)
      } else if (authData.session) {
        // Immediate access
        router.push(`/admin/onboarding?tenant=${formData.tenantSlug}`)
      }

    } catch (error: any) {
      console.error('Signup error:', error)
      setErrors({ 
        general: error.message || 'Signup failed. Please try again.' 
      })
    } finally {
      setIsLoading(false)
    }
  }

  const currentStepConfig = SIGNUP_STEPS.find(s => s.id === currentStep)

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-2xl">
        {/* Header */}
        <div className="text-center mb-8">
          <h2 className="text-3xl font-bold text-gray-900">
            Start Your Free Trial
          </h2>
          <p className="mt-2 text-gray-600">
            Join thousands of restaurants and hotels using BHEEMDINE
          </p>
        </div>

        {/* Progress Indicator */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            {SIGNUP_STEPS.map((step, index) => (
              <div key={step.id} className="flex items-center">
                <div className={`
                  w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium
                  ${currentStep >= step.id 
                    ? 'bg-orange-600 text-white' 
                    : 'bg-gray-200 text-gray-500'
                  }
                `}>
                  {currentStep > step.id ? (
                    <CheckCircle className="w-5 h-5" />
                  ) : (
                    step.id
                  )}
                </div>
                {index < SIGNUP_STEPS.length - 1 && (
                  <div className={`
                    h-1 w-full mx-2
                    ${currentStep > step.id ? 'bg-orange-600' : 'bg-gray-200'}
                  `} />
                )}
              </div>
            ))}
          </div>
          <div className="mt-3 text-center">
            <h3 className="text-lg font-medium text-gray-900">
              {currentStepConfig?.title}
            </h3>
            <p className="text-sm text-gray-600">
              {currentStepConfig?.description}
            </p>
          </div>
        </div>

        {/* Form */}
        <div className="bg-white py-8 px-4 shadow-lg sm:rounded-lg sm:px-10">
          {errors.general && (
            <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-md">
              <div className="flex">
                <AlertCircle className="w-5 h-5 text-red-400 mr-2" />
                <p className="text-sm text-red-600">{errors.general}</p>
              </div>
            </div>
          )}

          {/* Step 1: Business Information */}
          {currentStep === 1 && (
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Business Name *
                </label>
                <div className="mt-1 relative">
                  <Building2 className="absolute left-3 top-3 w-5 h-5 text-gray-400" />
                  <input
                    type="text"
                    value={formData.businessName}
                    onChange={(e) => handleInputChange('businessName', e.target.value)}
                    className={`
                      pl-10 w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500
                      ${errors.businessName ? 'border-red-300' : 'border-gray-300'}
                    `}
                    placeholder="Enter your business name"
                  />
                </div>
                {errors.businessName && (
                  <p className="mt-1 text-sm text-red-600">{errors.businessName}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Tenant Slug * 
                  <span className="text-gray-500 text-xs ml-1">(your unique URL identifier)</span>
                </label>
                <div className="mt-1 flex">
                  <span className="inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 text-sm">
                    bheemdine.com/
                  </span>
                  <input
                    type="text"
                    value={formData.tenantSlug}
                    onChange={(e) => handleInputChange('tenantSlug', e.target.value.toLowerCase())}
                    className={`
                      flex-1 px-3 py-2 border rounded-r-md shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500
                      ${errors.tenantSlug ? 'border-red-300' : 'border-gray-300'}
                    `}
                    placeholder="your-restaurant-name"
                  />
                </div>
                {slugAvailable === false && slugSuggestions.length > 0 && (
                  <div className="mt-2">
                    <p className="text-sm text-gray-600">Suggestions:</p>
                    <div className="flex flex-wrap gap-2 mt-1">
                      {slugSuggestions.map(suggestion => (
                        <button
                          key={suggestion}
                          onClick={() => handleInputChange('tenantSlug', suggestion)}
                          className="px-2 py-1 text-xs bg-orange-100 text-orange-700 rounded hover:bg-orange-200"
                        >
                          {suggestion}
                        </button>
                      ))}
                    </div>
                  </div>
                )}
                {slugAvailable === true && (
                  <p className="mt-1 text-sm text-green-600 flex items-center">
                    <CheckCircle className="w-4 h-4 mr-1" />
                    Available!
                  </p>
                )}
                {errors.tenantSlug && (
                  <p className="mt-1 text-sm text-red-600">{errors.tenantSlug}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Business Type *
                </label>
                <div className="mt-1 grid grid-cols-2 gap-3">
                  {BUSINESS_TYPES.map(type => (
                    <button
                      key={type.value}
                      type="button"
                      onClick={() => handleInputChange('businessType', type.value)}
                      className={`
                        p-3 border rounded-md text-left hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-orange-500
                        ${formData.businessType === type.value 
                          ? 'border-orange-500 bg-orange-50' 
                          : 'border-gray-300'
                        }
                      `}
                    >
                      <div className="text-lg mb-1">{type.icon}</div>
                      <div className="text-sm font-medium">{type.label}</div>
                    </button>
                  ))}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Business Description
                </label>
                <textarea
                  value={formData.businessDescription}
                  onChange={(e) => handleInputChange('businessDescription', e.target.value)}
                  rows={3}
                  className="mt-1 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                  placeholder="Tell us about your business..."
                />
              </div>
            </div>
          )}

          {/* Step 2: Contact Details */}
          {currentStep === 2 && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Business Email *
                  </label>
                  <div className="mt-1 relative">
                    <Mail className="absolute left-3 top-3 w-5 h-5 text-gray-400" />
                    <input
                      type="email"
                      value={formData.businessEmail}
                      onChange={(e) => handleInputChange('businessEmail', e.target.value)}
                      className={`
                        pl-10 w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500
                        ${errors.businessEmail ? 'border-red-300' : 'border-gray-300'}
                      `}
                      placeholder="<EMAIL>"
                    />
                  </div>
                  {errors.businessEmail && (
                    <p className="mt-1 text-sm text-red-600">{errors.businessEmail}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Business Phone *
                  </label>
                  <div className="mt-1 relative">
                    <Phone className="absolute left-3 top-3 w-5 h-5 text-gray-400" />
                    <input
                      type="tel"
                      value={formData.businessPhone}
                      onChange={(e) => handleInputChange('businessPhone', e.target.value)}
                      className={`
                        pl-10 w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500
                        ${errors.businessPhone ? 'border-red-300' : 'border-gray-300'}
                      `}
                      placeholder="+****************"
                    />
                  </div>
                  {errors.businessPhone && (
                    <p className="mt-1 text-sm text-red-600">{errors.businessPhone}</p>
                  )}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Website (Optional)
                </label>
                <div className="mt-1 relative">
                  <Globe className="absolute left-3 top-3 w-5 h-5 text-gray-400" />
                  <input
                    type="url"
                    value={formData.businessWebsite}
                    onChange={(e) => handleInputChange('businessWebsite', e.target.value)}
                    className="pl-10 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                    placeholder="https://yourrestaurant.com"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Business Address *
                </label>
                <div className="mt-1 relative">
                  <MapPin className="absolute left-3 top-3 w-5 h-5 text-gray-400" />
                  <input
                    type="text"
                    value={formData.businessAddress}
                    onChange={(e) => handleInputChange('businessAddress', e.target.value)}
                    className={`
                      pl-10 w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500
                      ${errors.businessAddress ? 'border-red-300' : 'border-gray-300'}
                    `}
                    placeholder="123 Main Street"
                  />
                </div>
                {errors.businessAddress && (
                  <p className="mt-1 text-sm text-red-600">{errors.businessAddress}</p>
                )}
              </div>

              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">City *</label>
                  <input
                    type="text"
                    value={formData.businessCity}
                    onChange={(e) => handleInputChange('businessCity', e.target.value)}
                    className={`
                      mt-1 w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500
                      ${errors.businessCity ? 'border-red-300' : 'border-gray-300'}
                    `}
                    placeholder="City"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">State *</label>
                  <input
                    type="text"
                    value={formData.businessState}
                    onChange={(e) => handleInputChange('businessState', e.target.value)}
                    className={`
                      mt-1 w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500
                      ${errors.businessState ? 'border-red-300' : 'border-gray-300'}
                    `}
                    placeholder="State"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">Country *</label>
                  <select
                    value={formData.businessCountry}
                    onChange={(e) => handleInputChange('businessCountry', e.target.value)}
                    className="mt-1 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                  >
                    <option value="US">United States</option>
                    <option value="CA">Canada</option>
                    <option value="GB">United Kingdom</option>
                    <option value="AU">Australia</option>
                    <option value="other">Other</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">ZIP/Postal *</label>
                  <input
                    type="text"
                    value={formData.businessZip}
                    onChange={(e) => handleInputChange('businessZip', e.target.value)}
                    className={`
                      mt-1 w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500
                      ${errors.businessZip ? 'border-red-300' : 'border-gray-300'}
                    `}
                    placeholder="ZIP"
                  />
                </div>
              </div>
            </div>
          )}

          {/* Step 3: Admin Account */}
          {currentStep === 3 && (
            <div className="space-y-6">
              {/* Google OAuth Option */}
              <div className="text-center">
                <button
                  type="button"
                  onClick={handleGoogleSignup}
                  disabled={isLoading}
                  className="w-full flex items-center justify-center px-4 py-3 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-orange-500 disabled:opacity-50"
                >
                  <Chrome className="w-5 h-5 mr-2 text-blue-500" />
                  Continue with Google
                </button>
                
                <div className="mt-4 relative">
                  <div className="absolute inset-0 flex items-center">
                    <div className="w-full border-t border-gray-300" />
                  </div>
                  <div className="relative flex justify-center text-sm">
                    <span className="px-2 bg-white text-gray-500">Or create account manually</span>
                  </div>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Admin Name *
                </label>
                <div className="mt-1 relative">
                  <User className="absolute left-3 top-3 w-5 h-5 text-gray-400" />
                  <input
                    type="text"
                    value={formData.adminName}
                    onChange={(e) => handleInputChange('adminName', e.target.value)}
                    className={`
                      pl-10 w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500
                      ${errors.adminName ? 'border-red-300' : 'border-gray-300'}
                    `}
                    placeholder="Your full name"
                  />
                </div>
                {errors.adminName && (
                  <p className="mt-1 text-sm text-red-600">{errors.adminName}</p>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Admin Email *
                  </label>
                  <div className="mt-1 relative">
                    <Mail className="absolute left-3 top-3 w-5 h-5 text-gray-400" />
                    <input
                      type="email"
                      value={formData.adminEmail}
                      onChange={(e) => handleInputChange('adminEmail', e.target.value)}
                      className={`
                        pl-10 w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500
                        ${errors.adminEmail ? 'border-red-300' : 'border-gray-300'}
                      `}
                      placeholder="<EMAIL>"
                    />
                  </div>
                  {errors.adminEmail && (
                    <p className="mt-1 text-sm text-red-600">{errors.adminEmail}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Admin Phone *
                  </label>
                  <div className="mt-1 relative">
                    <Phone className="absolute left-3 top-3 w-5 h-5 text-gray-400" />
                    <input
                      type="tel"
                      value={formData.adminPhone}
                      onChange={(e) => handleInputChange('adminPhone', e.target.value)}
                      className={`
                        pl-10 w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500
                        ${errors.adminPhone ? 'border-red-300' : 'border-gray-300'}
                      `}
                      placeholder="+****************"
                    />
                  </div>
                  {errors.adminPhone && (
                    <p className="mt-1 text-sm text-red-600">{errors.adminPhone}</p>
                  )}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Password *
                </label>
                <div className="mt-1 relative">
                  <Lock className="absolute left-3 top-3 w-5 h-5 text-gray-400" />
                  <input
                    type={showPassword ? 'text' : 'password'}
                    value={formData.adminPassword}
                    onChange={(e) => handleInputChange('adminPassword', e.target.value)}
                    className={`
                      pl-10 pr-10 w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500
                      ${errors.adminPassword ? 'border-red-300' : 'border-gray-300'}
                    `}
                    placeholder="Create a strong password"
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-3 text-gray-400 hover:text-gray-600"
                  >
                    {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                  </button>
                </div>
                {errors.adminPassword && (
                  <p className="mt-1 text-sm text-red-600">{errors.adminPassword}</p>
                )}
                <div className="mt-2 text-xs text-gray-500">
                  Must be at least 12 characters with uppercase, lowercase, number, and special character
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Confirm Password *
                </label>
                <div className="mt-1 relative">
                  <Lock className="absolute left-3 top-3 w-5 h-5 text-gray-400" />
                  <input
                    type={showConfirmPassword ? 'text' : 'password'}
                    value={formData.confirmPassword}
                    onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                    className={`
                      pl-10 pr-10 w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500
                      ${errors.confirmPassword ? 'border-red-300' : 'border-gray-300'}
                    `}
                    placeholder="Confirm your password"
                  />
                  <button
                    type="button"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    className="absolute right-3 top-3 text-gray-400 hover:text-gray-600"
                  >
                    {showConfirmPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                  </button>
                </div>
                {errors.confirmPassword && (
                  <p className="mt-1 text-sm text-red-600">{errors.confirmPassword}</p>
                )}
              </div>
            </div>
          )}

          {/* Step 4: Preferences */}
          {currentStep === 4 && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Timezone *
                  </label>
                  <select
                    value={formData.timezone}
                    onChange={(e) => handleInputChange('timezone', e.target.value)}
                    className="mt-1 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                  >
                    <option value="America/New_York">Eastern Time</option>
                    <option value="America/Chicago">Central Time</option>
                    <option value="America/Denver">Mountain Time</option>
                    <option value="America/Los_Angeles">Pacific Time</option>
                    <option value="UTC">UTC</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Currency *
                  </label>
                  <select
                    value={formData.currency}
                    onChange={(e) => handleInputChange('currency', e.target.value)}
                    className="mt-1 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                  >
                    <option value="USD">USD ($)</option>
                    <option value="EUR">EUR (€)</option>
                    <option value="GBP">GBP (£)</option>
                    <option value="CAD">CAD ($)</option>
                    <option value="AUD">AUD ($)</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Language *
                  </label>
                  <select
                    value={formData.language}
                    onChange={(e) => handleInputChange('language', e.target.value)}
                    className="mt-1 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                  >
                    <option value="en">English</option>
                    <option value="es">Spanish</option>
                    <option value="fr">French</option>
                    <option value="de">German</option>
                  </select>
                </div>
              </div>

              {/* Legal Agreements */}
              <div className="space-y-4 pt-6 border-t border-gray-200">
                <div className="flex items-start">
                  <input
                    type="checkbox"
                    checked={formData.agreeToTerms}
                    onChange={(e) => handleInputChange('agreeToTerms', e.target.checked)}
                    className="mt-1 h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded"
                  />
                  <div className="ml-3">
                    <label className="text-sm text-gray-700">
                      I agree to the{' '}
                      <a href="/legal/terms" target="_blank" className="text-orange-600 hover:text-orange-700 underline">
                        Terms of Service
                      </a>{' '}*
                    </label>
                    {errors.agreeToTerms && (
                      <p className="mt-1 text-sm text-red-600">{errors.agreeToTerms}</p>
                    )}
                  </div>
                </div>

                <div className="flex items-start">
                  <input
                    type="checkbox"
                    checked={formData.agreeToPrivacy}
                    onChange={(e) => handleInputChange('agreeToPrivacy', e.target.checked)}
                    className="mt-1 h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded"
                  />
                  <div className="ml-3">
                    <label className="text-sm text-gray-700">
                      I agree to the{' '}
                      <a href="/legal/privacy" target="_blank" className="text-orange-600 hover:text-orange-700 underline">
                        Privacy Policy
                      </a>{' '}*
                    </label>
                    {errors.agreeToPrivacy && (
                      <p className="mt-1 text-sm text-red-600">{errors.agreeToPrivacy}</p>
                    )}
                  </div>
                </div>

                <div className="flex items-start">
                  <input
                    type="checkbox"
                    checked={formData.allowMarketing}
                    onChange={(e) => handleInputChange('allowMarketing', e.target.checked)}
                    className="mt-1 h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded"
                  />
                  <div className="ml-3">
                    <label className="text-sm text-gray-700">
                      I would like to receive product updates and marketing communications
                    </label>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Navigation Buttons */}
          <div className="mt-8 flex justify-between">
            <button
              type="button"
              onClick={handlePrevStep}
              disabled={currentStep === 1}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-orange-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Previous
            </button>

            {currentStep < SIGNUP_STEPS.length ? (
              <button
                type="button"
                onClick={handleNextStep}
                className="px-6 py-2 text-sm font-medium text-white bg-orange-600 border border-transparent rounded-md hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-orange-500"
              >
                Next
              </button>
            ) : (
              <button
                type="button"
                onClick={handleSubmit}
                disabled={isLoading}
                className="px-6 py-2 text-sm font-medium text-white bg-orange-600 border border-transparent rounded-md hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-orange-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? 'Creating Account...' : 'Start Free Trial'}
              </button>
            )}
          </div>

          {/* Footer */}
          <div className="mt-6 text-center text-sm text-gray-600">
            Already have an account?{' '}
            <button
              onClick={() => router.push('/login')}
              className="text-orange-600 hover:text-orange-700 font-medium"
            >
              Sign in here
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}