/**
 * B2B Tenant Admin Login Form with Enhanced Security
 * Multi-tenant authentication with Google OAuth support
 */

'use client'

import React, { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { 
  Mail, 
  Lock, 
  Building2, 
  Eye, 
  EyeOff, 
  AlertCircle, 
  CheckCircle,
  Chrome,
  Shield,
  Loader2
} from 'lucide-react'
import { supabase, authClient } from '@/utils/supabase/client'

interface LoginFormData {
  email: string
  password: string
  tenantSlug: string
  rememberMe: boolean
}

interface LoginErrors {
  email?: string
  password?: string
  tenantSlug?: string
  general?: string
}

export default function TenantLoginForm() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [isLoading, setIsLoading] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [errors, setErrors] = useState<LoginErrors>({})
  const [tenantSuggestions, setTenantSuggestions] = useState<string[]>([])

  const [formData, setFormData] = useState<LoginFormData>({
    email: '',
    password: '',
    tenantSlug: searchParams?.get('tenant') || '',
    rememberMe: false
  })

  // Auto-redirect if already authenticated
  useEffect(() => {
    const checkAuthStatus = async () => {
      const { session } = await authClient.getSessionWithTenant()
      if (session) {
        const redirectUrl = searchParams?.get('redirect') || `/admin/dashboard${formData.tenantSlug ? `?tenant=${formData.tenantSlug}` : ''}`
        router.push(redirectUrl)
      }
    }

    checkAuthStatus()
  }, [])

  // Get tenant suggestions based on email
  useEffect(() => {
    const getTenantSuggestions = async () => {
      if (formData.email && formData.email.includes('@')) {
        try {
          const { data: adminUsers } = await supabase
            .from('AdminUser')
            .select(`
              tenant:Tenant(slug, name)
            `)
            .eq('email', formData.email)
            .eq('status', 'ACTIVE')
            .limit(5)

          const suggestions = adminUsers
            ?.map(user => user.tenant)
            .filter(Boolean)
            .map(tenant => tenant.slug) || []

          setTenantSuggestions(suggestions)
        } catch (error) {
          console.error('Error fetching tenant suggestions:', error)
        }
      } else {
        setTenantSuggestions([])
      }
    }

    const timeoutId = setTimeout(getTenantSuggestions, 500)
    return () => clearTimeout(timeoutId)
  }, [formData.email])

  const validateForm = (): boolean => {
    const newErrors: LoginErrors = {}

    if (!formData.email) {
      newErrors.email = 'Email is required'
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address'
    }

    if (!formData.password) {
      newErrors.password = 'Password is required'
    }

    if (!formData.tenantSlug) {
      newErrors.tenantSlug = 'Tenant is required'
    } else if (!/^[a-z0-9-]+$/.test(formData.tenantSlug)) {
      newErrors.tenantSlug = 'Invalid tenant format'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleInputChange = (field: keyof LoginFormData, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    
    // Clear error for this field
    if (errors[field as keyof LoginErrors]) {
      setErrors(prev => ({ ...prev, [field]: undefined }))
    }
  }

  const handleGoogleLogin = async () => {
    try {
      setIsLoading(true)
      setErrors({})

      const redirectTo = `${window.location.origin}/auth/callback${formData.tenantSlug ? `?tenant=${formData.tenantSlug}` : ''}`

      const { error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo,
          queryParams: {
            tenant_slug: formData.tenantSlug
          }
        }
      })

      if (error) throw error
    } catch (error: any) {
      console.error('Google login error:', error)
      setErrors({ 
        general: 'Google login failed. Please try again or use email/password.' 
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleEmailLogin = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) return

    try {
      setIsLoading(true)
      setErrors({})

      const { data, error } = await authClient.signInWithTenant(
        formData.email,
        formData.password,
        formData.tenantSlug
      )

      if (error) {
        // Handle specific error types
        if (error.message?.includes('Invalid login credentials')) {
          setErrors({ general: 'Invalid email or password' })
        } else if (error.message?.includes('Email not confirmed')) {
          setErrors({ 
            general: 'Please verify your email address before logging in',
            email: 'Email not verified'
          })
        } else if (error.message?.includes('Too many requests')) {
          setErrors({ general: 'Too many login attempts. Please try again later.' })
        } else {
          setErrors({ general: error.message || 'Login failed. Please try again.' })
        }
        return
      }

      // Verify tenant access
      const { data: adminUser } = await supabase
        .from('AdminUser')
        .select(`
          *,
          tenant:Tenant(*)
        `)
        .eq('authUserId', data.user?.id)
        .eq('status', 'ACTIVE')
        .single()

      if (!adminUser) {
        setErrors({ general: 'Account not found or inactive' })
        await authClient.signOut()
        return
      }

      if (adminUser.tenant.slug !== formData.tenantSlug) {
        setErrors({ general: 'You do not have access to this tenant' })
        await authClient.signOut()
        return
      }

      if (!adminUser.tenant.isActive) {
        setErrors({ general: 'Tenant account is suspended. Please contact support.' })
        await authClient.signOut()
        return
      }

      // Successful login - redirect to dashboard
      const redirectUrl = searchParams?.get('redirect') || `/admin/dashboard?tenant=${formData.tenantSlug}`
      router.push(redirectUrl)

    } catch (error: any) {
      console.error('Login error:', error)
      setErrors({ 
        general: 'An unexpected error occurred. Please try again.' 
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleForgotPassword = async () => {
    if (!formData.email) {
      setErrors({ email: 'Please enter your email address first' })
      return
    }

    try {
      setIsLoading(true)
      
      const { error } = await supabase.auth.resetPasswordForEmail(formData.email, {
        redirectTo: `${window.location.origin}/auth/reset-password?tenant=${formData.tenantSlug}`
      })

      if (error) throw error

      alert('Password reset email sent! Check your inbox.')
    } catch (error: any) {
      setErrors({ general: 'Failed to send password reset email. Please try again.' })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        {/* Header */}
        <div className="text-center">
          <div className="flex justify-center mb-6">
            <div className="w-16 h-16 bg-orange-600 rounded-2xl flex items-center justify-center">
              <Building2 className="w-8 h-8 text-white" />
            </div>
          </div>
          <h2 className="text-3xl font-bold text-gray-900">
            Admin Login
          </h2>
          <p className="mt-2 text-gray-600">
            Sign in to your restaurant management dashboard
          </p>
        </div>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow-lg sm:rounded-lg sm:px-10">
          {/* Error Alert */}
          {errors.general && (
            <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-md">
              <div className="flex">
                <AlertCircle className="w-5 h-5 text-red-400 mr-2 flex-shrink-0" />
                <div>
                  <p className="text-sm text-red-600">{errors.general}</p>
                </div>
              </div>
            </div>
          )}

          {/* Google OAuth Login */}
          <div className="mb-6">
            <button
              type="button"
              onClick={handleGoogleLogin}
              disabled={isLoading || !formData.tenantSlug}
              className="w-full flex items-center justify-center px-4 py-3 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-orange-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <Loader2 className="w-5 h-5 mr-2 animate-spin" />
              ) : (
                <Chrome className="w-5 h-5 mr-2 text-blue-500" />
              )}
              Continue with Google
            </button>
            
            {!formData.tenantSlug && (
              <p className="mt-2 text-xs text-gray-500 text-center">
                Please enter your tenant first
              </p>
            )}

            <div className="mt-6 relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-300" />
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-2 bg-white text-gray-500">Or continue with email</span>
              </div>
            </div>
          </div>

          {/* Email/Password Form */}
          <form onSubmit={handleEmailLogin} className="space-y-6">
            {/* Tenant Slug */}
            <div>
              <label htmlFor="tenantSlug" className="block text-sm font-medium text-gray-700">
                Restaurant/Hotel Tenant *
              </label>
              <div className="mt-1 relative">
                <Building2 className="absolute left-3 top-3 w-5 h-5 text-gray-400" />
                <input
                  id="tenantSlug"
                  type="text"
                  value={formData.tenantSlug}
                  onChange={(e) => handleInputChange('tenantSlug', e.target.value.toLowerCase())}
                  className={`
                    pl-10 w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500
                    ${errors.tenantSlug ? 'border-red-300' : 'border-gray-300'}
                  `}
                  placeholder="your-restaurant-name"
                  required
                />
              </div>
              {errors.tenantSlug && (
                <p className="mt-1 text-sm text-red-600">{errors.tenantSlug}</p>
              )}
              <div className="mt-1 text-xs text-gray-500">
                Your unique tenant identifier (e.g., "my-restaurant")
              </div>
            </div>

            {/* Email */}
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                Email Address *
              </label>
              <div className="mt-1 relative">
                <Mail className="absolute left-3 top-3 w-5 h-5 text-gray-400" />
                <input
                  id="email"
                  type="email"
                  autoComplete="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  className={`
                    pl-10 w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500
                    ${errors.email ? 'border-red-300' : 'border-gray-300'}
                  `}
                  placeholder="<EMAIL>"
                  required
                />
              </div>
              {errors.email && (
                <p className="mt-1 text-sm text-red-600">{errors.email}</p>
              )}
              
              {/* Tenant Suggestions */}
              {tenantSuggestions.length > 0 && !formData.tenantSlug && (
                <div className="mt-2">
                  <p className="text-xs text-gray-600">Your accessible tenants:</p>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {tenantSuggestions.map(suggestion => (
                      <button
                        key={suggestion}
                        type="button"
                        onClick={() => handleInputChange('tenantSlug', suggestion)}
                        className="px-2 py-1 text-xs bg-orange-100 text-orange-700 rounded hover:bg-orange-200"
                      >
                        {suggestion}
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Password */}
            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                Password *
              </label>
              <div className="mt-1 relative">
                <Lock className="absolute left-3 top-3 w-5 h-5 text-gray-400" />
                <input
                  id="password"
                  type={showPassword ? 'text' : 'password'}
                  autoComplete="current-password"
                  value={formData.password}
                  onChange={(e) => handleInputChange('password', e.target.value)}
                  className={`
                    pl-10 pr-10 w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500
                    ${errors.password ? 'border-red-300' : 'border-gray-300'}
                  `}
                  placeholder="Enter your password"
                  required
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-3 text-gray-400 hover:text-gray-600"
                >
                  {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                </button>
              </div>
              {errors.password && (
                <p className="mt-1 text-sm text-red-600">{errors.password}</p>
              )}
            </div>

            {/* Remember Me & Forgot Password */}
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <input
                  id="rememberMe"
                  type="checkbox"
                  checked={formData.rememberMe}
                  onChange={(e) => handleInputChange('rememberMe', e.target.checked)}
                  className="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded"
                />
                <label htmlFor="rememberMe" className="ml-2 block text-sm text-gray-700">
                  Remember me
                </label>
              </div>

              <button
                type="button"
                onClick={handleForgotPassword}
                disabled={isLoading}
                className="text-sm text-orange-600 hover:text-orange-700 disabled:opacity-50"
              >
                Forgot password?
              </button>
            </div>

            {/* Login Button */}
            <button
              type="submit"
              disabled={isLoading}
              className="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                  Signing in...
                </>
              ) : (
                'Sign in to Dashboard'
              )}
            </button>
          </form>

          {/* Security Notice */}
          <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-md">
            <div className="flex">
              <Shield className="w-5 h-5 text-blue-400 mr-2 flex-shrink-0" />
              <div>
                <h4 className="text-sm font-medium text-blue-800">
                  Enterprise Security
                </h4>
                <p className="text-xs text-blue-600 mt-1">
                  Your login is protected by enterprise-grade security with audit logging and multi-factor authentication.
                </p>
              </div>
            </div>
          </div>

          {/* Footer Links */}
          <div className="mt-6 text-center space-y-2">
            <p className="text-sm text-gray-600">
              Don't have an account?{' '}
              <button
                onClick={() => router.push('/signup')}
                className="text-orange-600 hover:text-orange-700 font-medium"
              >
                Start your free trial
              </button>
            </p>
            
            <div className="flex justify-center space-x-4 text-xs text-gray-500">
              <a href="/legal/privacy" className="hover:text-gray-700">Privacy Policy</a>
              <span>•</span>
              <a href="/legal/terms" className="hover:text-gray-700">Terms of Service</a>
              <span>•</span>
              <a href="/support" className="hover:text-gray-700">Support</a>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}