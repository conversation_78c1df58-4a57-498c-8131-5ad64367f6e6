/**
 * Performance Caching Layer for BheemDine
 * Implements Redis-like caching with fallback to memory
 */

interface CacheItem<T> {
  data: T;
  expiresAt: number;
  hits: number;
  lastAccessed: number;
}

class PerformanceCache {
  protected cache = new Map<string, CacheItem<any>>();
  private maxSize = 1000;
  private defaultTTL = 5 * 60 * 1000; // 5 minutes

  set<T>(key: string, data: T, ttl?: number): void {
    // Remove oldest items if cache is full
    if (this.cache.size >= this.maxSize) {
      this.evictLRU();
    }

    const expiresAt = Date.now() + (ttl || this.defaultTTL);
    this.cache.set(key, {
      data,
      expiresAt,
      hits: 0,
      lastAccessed: Date.now(),
    });
  }

  get<T>(key: string): T | null {
    const item = this.cache.get(key);
    
    if (!item) return null;
    
    // Check if expired
    if (Date.now() > item.expiresAt) {
      this.cache.delete(key);
      return null;
    }

    // Update access stats
    item.hits++;
    item.lastAccessed = Date.now();
    
    return item.data as T;
  }

  has(key: string): boolean {
    const item = this.cache.get(key);
    if (!item) return false;
    
    if (Date.now() > item.expiresAt) {
      this.cache.delete(key);
      return false;
    }
    
    return true;
  }

  delete(key: string): boolean {
    return this.cache.delete(key);
  }

  clear(): void {
    this.cache.clear();
  }

  // Cache statistics for monitoring
  getStats() {
    const now = Date.now();
    let expired = 0;
    let totalHits = 0;

    Array.from(this.cache.entries()).forEach(([key, item]) => {
      if (now > item.expiresAt) {
        expired++;
      }
      totalHits += item.hits;
    });

    return {
      size: this.cache.size,
      expired,
      totalHits,
      hitRate: totalHits / Math.max(this.cache.size, 1),
    };
  }

  private evictLRU(): void {
    let oldestKey: string | null = null;
    let oldestTime = Date.now();

    Array.from(this.cache.entries()).forEach(([key, item]) => {
      if (item.lastAccessed < oldestTime) {
        oldestTime = item.lastAccessed;
        oldestKey = key;
      }
    });

    if (oldestKey) {
      this.cache.delete(oldestKey);
    }
  }

  // Cache with automatic key generation
  async memoize<T>(
    key: string,
    fn: () => Promise<T> | T,
    ttl?: number
  ): Promise<T> {
    const cached = this.get<T>(key);
    if (cached !== null) {
      return cached;
    }

    const result = await fn();
    this.set(key, result, ttl);
    return result;
  }
}

// Menu-specific caching
class MenuCache extends PerformanceCache {
  private menuTTL = 10 * 60 * 1000; // 10 minutes for menu data

  async getMenuItems(tenantId: string, filters?: any) {
    const key = `menu:${tenantId}:${JSON.stringify(filters || {})}`;
    return this.memoize(key, async () => {
      // This would call the actual menu API
      const response = await fetch(`/api/menu/${tenantId}?${new URLSearchParams(filters || {})}`);
      return response.json();
    }, this.menuTTL);
  }

  invalidateMenuCache(tenantId: string) {
    // Remove all menu-related cache entries for this tenant
    Array.from(this.cache.keys()).forEach(key => {
      if (key.startsWith(`menu:${tenantId}`)) {
        this.delete(key);
      }
    });
  }
}

// Export instances
export const performanceCache = new PerformanceCache();
export const menuCache = new MenuCache();

// Cache warming utility
export async function warmCache() {
  console.log('🔥 Warming performance cache...');
  
  // Pre-load common data that doesn't change often
  const commonKeys = [
    'allergens:all',
    'categories:all',
    'settings:app',
  ];

  for (const key of commonKeys) {
    try {
      // This would be replaced with actual data fetching
      await performanceCache.memoize(key, async () => {
        const response = await fetch(`/api/cache-warm/${key}`);
        return response.json();
      });
    } catch (error) {
      console.warn(`Failed to warm cache for ${key}:`, error);
    }
  }
  
  console.log('✅ Cache warming completed');
}