/**
 * Database Performance Optimizations for BheemDine
 * Includes connection pooling, query optimization, and monitoring
 */

import { PrismaClient } from '@prisma/client';

// Production-optimized Prisma configuration
const createPrismaClient = () => {
  const isProduction = process.env.NODE_ENV === 'production';
  
  return new PrismaClient({
    log: isProduction 
      ? ['error'] 
      : ['query', 'error'],
    
    datasources: {
      db: {
        url: process.env.DATABASE_URL,
      },
    },
  });
};

// Singleton pattern for database connection
let prismaInstance: PrismaClient | null = null;

export function getPrismaClient(): PrismaClient {
  if (!prismaInstance) {
    prismaInstance = createPrismaClient();
    
    // Graceful shutdown handling
    process.on('SIGTERM', async () => {
      await prismaInstance?.$disconnect();
    });
    
    process.on('SIGINT', async () => {
      await prismaInstance?.$disconnect();
    });
  }
  
  return prismaInstance;
}

// Database performance utilities
export class DatabaseOptimizer {
  private prisma: PrismaClient;
  
  constructor() {
    this.prisma = getPrismaClient();
  }

  // Health check
  async healthCheck(): Promise<{ healthy: boolean; latency: number }> {
    const start = Date.now();
    
    try {
      await this.prisma.$queryRaw`SELECT 1`;
      const latency = Date.now() - start;
      
      return {
        healthy: true,
        latency,
      };
    } catch (error) {
      return {
        healthy: false,
        latency: Date.now() - start,
      };
    }
  }

  // Performance monitoring
  async getDatabaseStats() {
    try {
      const [
        totalMenuItems,
        totalOrders
      ] = await Promise.all([
        this.prisma.menuItem.count(),
        this.prisma.order.count()
      ]);

      return {
        totalMenuItems,
        totalOrders,
        timestamp: new Date(),
      };
    } catch (error) {
      return {
        totalMenuItems: 0,
        totalOrders: 0,
        timestamp: new Date(),
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  // Connection pool status
  async getConnectionStatus() {
    try {
      const result = await this.prisma.$queryRaw`SELECT 1 as connected`;
      return { connected: true, error: null };
    } catch (error) {
      return { 
        connected: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }
}

// Export singleton instance
export const dbOptimizer = new DatabaseOptimizer();

// Database indexes creation utility
export async function createPerformanceIndexes() {
  const prisma = getPrismaClient();
  
  try {
    // Create indexes for better query performance
    await prisma.$executeRaw`
      CREATE INDEX IF NOT EXISTS idx_menu_item_tenant 
      ON "MenuItem" ("tenantId");
    `;
    
    await prisma.$executeRaw`
      CREATE INDEX IF NOT EXISTS idx_order_tenant_created 
      ON "Order" ("tenantId", "createdAt");
    `;
    
    console.log('✅ Database performance indexes created');
  } catch (error) {
    console.warn('⚠️ Some database indexes may already exist:', error);
  }
}