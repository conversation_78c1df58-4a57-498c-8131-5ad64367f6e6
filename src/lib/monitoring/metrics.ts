/**
 * Comprehensive Monitoring & Metrics for BheemDine
 * Performance, health checks, error tracking, and business metrics
 */

export interface PerformanceMetric {
  name: string;
  value: number;
  unit: 'ms' | 'bytes' | 'count' | 'percentage';
  timestamp: Date;
  labels?: Record<string, string>;
}

export interface HealthCheck {
  service: string;
  status: 'healthy' | 'unhealthy' | 'degraded';
  latency: number;
  message?: string;
  timestamp: Date;
}

export interface ErrorEvent {
  id: string;
  message: string;
  stack: string;
  level: 'error' | 'warning' | 'info';
  context: {
    userId?: string;
    tenantId?: string;
    route?: string;
    userAgent?: string;
    ip?: string;
    operation?: string;
    query?: string;
  };
  timestamp: Date;
}

export interface BusinessMetric {
  metric: string;
  value: number;
  timestamp: Date;
  dimensions: Record<string, string>;
}

class MonitoringService {
  private metrics: PerformanceMetric[] = [];
  private errors: ErrorEvent[] = [];
  private healthChecks: HealthCheck[] = [];
  private businessMetrics: BusinessMetric[] = [];

  // Performance Monitoring
  recordMetric(metric: Omit<PerformanceMetric, 'timestamp'>): void {
    const fullMetric: PerformanceMetric = {
      ...metric,
      timestamp: new Date(),
    };
    
    this.metrics.push(fullMetric);
    
    // Keep only last 1000 metrics in memory
    if (this.metrics.length > 1000) {
      this.metrics = this.metrics.slice(-1000);
    }
    
    // Send to monitoring backend
    this.sendMetricToBackend(fullMetric);
  }

  // API Response Time Monitoring
  async measureApiCall<T>(
    operation: string,
    apiCall: () => Promise<T>
  ): Promise<T> {
    const startTime = performance.now();
    const startMemory = this.getMemoryUsage();
    
    try {
      const result = await apiCall();
      
      const duration = performance.now() - startTime;
      const endMemory = this.getMemoryUsage();
      
      this.recordMetric({
        name: `api.${operation}.duration`,
        value: duration,
        unit: 'ms',
        labels: { status: 'success' },
      });
      
      this.recordMetric({
        name: `api.${operation}.memory_delta`,
        value: endMemory - startMemory,
        unit: 'bytes',
        labels: { status: 'success' },
      });
      
      return result;
    } catch (error) {
      const duration = performance.now() - startTime;
      
      this.recordMetric({
        name: `api.${operation}.duration`,
        value: duration,
        unit: 'ms',
        labels: { status: 'error' },
      });
      
      this.recordError({
        message: `API call failed: ${operation}`,
        stack: error instanceof Error ? error.stack || '' : '',
        level: 'error',
        context: { operation },
      });
      
      throw error;
    }
  }

  // Database Query Monitoring
  async measureDbQuery<T>(
    query: string,
    operation: () => Promise<T>
  ): Promise<T> {
    const startTime = performance.now();
    
    try {
      const result = await operation();
      const duration = performance.now() - startTime;
      
      this.recordMetric({
        name: 'database.query.duration',
        value: duration,
        unit: 'ms',
        labels: { 
          query: this.sanitizeQuery(query),
          status: 'success',
        },
      });
      
      return result;
    } catch (error) {
      const duration = performance.now() - startTime;
      
      this.recordMetric({
        name: 'database.query.duration',
        value: duration,
        unit: 'ms',
        labels: { 
          query: this.sanitizeQuery(query),
          status: 'error',
        },
      });
      
      this.recordError({
        message: `Database query failed: ${query}`,
        stack: error instanceof Error ? error.stack || '' : '',
        level: 'error',
        context: { query },
      });
      
      throw error;
    }
  }

  // Error Tracking
  recordError(error: Omit<ErrorEvent, 'id' | 'timestamp'>): void {
    const fullError: ErrorEvent = {
      ...error,
      id: this.generateErrorId(),
      timestamp: new Date(),
    };
    
    this.errors.push(fullError);
    
    // Keep only last 500 errors in memory
    if (this.errors.length > 500) {
      this.errors = this.errors.slice(-500);
    }
    
    // Send to error tracking service
    this.sendErrorToBackend(fullError);
    
    // Alert on critical errors
    if (error.level === 'error') {
      this.triggerErrorAlert(fullError);
    }
  }

  // Health Checks
  async runHealthChecks(): Promise<HealthCheck[]> {
    const checks = await Promise.allSettled([
      this.checkDatabase(),
      this.checkSupabase(),
      this.checkMemoryUsage(),
      this.checkDiskSpace(),
      this.checkAPIEndpoints(),
    ]);
    
    const healthChecks = checks
      .map((result, index) => {
        if (result.status === 'fulfilled') {
          return result.value;
        } else {
          return {
            service: ['database', 'supabase', 'memory', 'disk', 'api'][index],
            status: 'unhealthy' as const,
            latency: 0,
            message: result.reason?.message || 'Health check failed',
            timestamp: new Date(),
          };
        }
      });
    
    this.healthChecks = healthChecks;
    return healthChecks;
  }

  // Business Metrics
  recordBusinessMetric(metric: Omit<BusinessMetric, 'timestamp'>): void {
    const fullMetric: BusinessMetric = {
      ...metric,
      timestamp: new Date(),
    };
    
    this.businessMetrics.push(fullMetric);
    this.sendBusinessMetricToBackend(fullMetric);
  }

  // Real-time Metrics Dashboard Data
  getDashboardMetrics(): {
    performance: PerformanceMetric[];
    errors: ErrorEvent[];
    health: HealthCheck[];
    business: BusinessMetric[];
    summary: {
      errorRate: number;
      avgResponseTime: number;
      healthScore: number;
      uptime: number;
    };
  } {
    const now = Date.now();
    const oneHourAgo = now - 60 * 60 * 1000;
    
    // Recent metrics
    const recentMetrics = this.metrics.filter(
      m => m.timestamp.getTime() > oneHourAgo
    );
    
    const recentErrors = this.errors.filter(
      e => e.timestamp.getTime() > oneHourAgo
    );
    
    // Calculate summary statistics
    const apiCalls = recentMetrics.filter(m => m.name.includes('api.'));
    const avgResponseTime = apiCalls.length > 0
      ? apiCalls.reduce((sum, m) => sum + m.value, 0) / apiCalls.length
      : 0;
    
    const errorRate = apiCalls.length > 0
      ? (recentErrors.length / apiCalls.length) * 100
      : 0;
    
    const healthyServices = this.healthChecks.filter(h => h.status === 'healthy').length;
    const healthScore = this.healthChecks.length > 0
      ? (healthyServices / this.healthChecks.length) * 100
      : 100;
    
    return {
      performance: recentMetrics.slice(-50),
      errors: recentErrors.slice(-20),
      health: this.healthChecks,
      business: this.businessMetrics.slice(-20),
      summary: {
        errorRate,
        avgResponseTime,
        healthScore,
        uptime: this.calculateUptime(),
      },
    };
  }

  // Alert System
  private async triggerErrorAlert(error: ErrorEvent): Promise<void> {
    // Check if this is a critical error that needs immediate attention
    const criticalPatterns = [
      'database',
      'payment',
      'authentication',
      'security',
    ];
    
    const isCritical = criticalPatterns.some(pattern =>
      error.message.toLowerCase().includes(pattern)
    );
    
    if (isCritical) {
      await this.sendCriticalAlert(error);
    }
  }

  // Health Check Implementations
  private async checkDatabase(): Promise<HealthCheck> {
    const startTime = performance.now();
    
    try {
      // This would use your actual database client
      await new Promise(resolve => setTimeout(resolve, Math.random() * 50));
      
      const latency = performance.now() - startTime;
      
      return {
        service: 'database',
        status: latency < 100 ? 'healthy' : latency < 200 ? 'degraded' : 'unhealthy',
        latency,
        timestamp: new Date(),
      };
    } catch (error) {
      return {
        service: 'database',
        status: 'unhealthy',
        latency: performance.now() - startTime,
        message: error instanceof Error ? error.message : 'Database check failed',
        timestamp: new Date(),
      };
    }
  }

  private async checkSupabase(): Promise<HealthCheck> {
    const startTime = performance.now();
    
    try {
      // This would ping your Supabase instance
      const response = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/`, {
        headers: {
          'Authorization': `Bearer ${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
        },
      });
      
      const latency = performance.now() - startTime;
      
      return {
        service: 'supabase',
        status: response.ok ? 'healthy' : 'unhealthy',
        latency,
        message: response.ok ? undefined : `HTTP ${response.status}`,
        timestamp: new Date(),
      };
    } catch (error) {
      return {
        service: 'supabase',
        status: 'unhealthy',
        latency: performance.now() - startTime,
        message: error instanceof Error ? error.message : 'Supabase check failed',
        timestamp: new Date(),
      };
    }
  }

  private async checkMemoryUsage(): Promise<HealthCheck> {
    const memoryUsage = this.getMemoryUsage();
    const memoryLimitMB = 512; // Adjust based on your deployment
    const usagePercentage = (memoryUsage / (memoryLimitMB * 1024 * 1024)) * 100;
    
    return {
      service: 'memory',
      status: usagePercentage < 80 ? 'healthy' : usagePercentage < 90 ? 'degraded' : 'unhealthy',
      latency: 0,
      message: `${usagePercentage.toFixed(1)}% usage`,
      timestamp: new Date(),
    };
  }

  private async checkDiskSpace(): Promise<HealthCheck> {
    // This would check actual disk space in a real implementation
    return {
      service: 'disk',
      status: 'healthy',
      latency: 0,
      message: 'Sufficient space available',
      timestamp: new Date(),
    };
  }

  private async checkAPIEndpoints(): Promise<HealthCheck> {
    const startTime = performance.now();
    
    try {
      // Test a lightweight endpoint
      const response = await fetch('/api/health');
      const latency = performance.now() - startTime;
      
      return {
        service: 'api',
        status: response.ok ? 'healthy' : 'unhealthy',
        latency,
        message: response.ok ? undefined : `HTTP ${response.status}`,
        timestamp: new Date(),
      };
    } catch (error) {
      return {
        service: 'api',
        status: 'unhealthy',
        latency: performance.now() - startTime,
        message: error instanceof Error ? error.message : 'API check failed',
        timestamp: new Date(),
      };
    }
  }

  // Utility Methods
  private getMemoryUsage(): number {
    if (typeof process !== 'undefined' && process.memoryUsage) {
      return process.memoryUsage().heapUsed;
    }
    return 0;
  }

  private sanitizeQuery(query: string): string {
    // Remove sensitive data from query strings for logging
    return query.replace(/\b\d{16,19}\b/g, '[CARD]')
                .replace(/\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g, '[EMAIL]')
                .substring(0, 100);
  }

  private generateErrorId(): string {
    return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private calculateUptime(): number {
    // This would calculate actual uptime in a real implementation
    return 99.9;
  }

  private async sendMetricToBackend(metric: PerformanceMetric): Promise<void> {
    // Send to monitoring service (DataDog, New Relic, etc.)
    console.log('📊 Metric:', metric);
  }

  private async sendErrorToBackend(error: ErrorEvent): Promise<void> {
    // Send to error tracking service (Sentry, Bugsnag, etc.)
    console.error('🚨 Error:', error);
  }

  private async sendBusinessMetricToBackend(metric: BusinessMetric): Promise<void> {
    // Send to analytics service
    console.log('📈 Business Metric:', metric);
  }

  private async sendCriticalAlert(error: ErrorEvent): Promise<void> {
    // Send critical alert (PagerDuty, Slack, email, etc.)
    console.error('🚨 CRITICAL ALERT:', error);
  }
}

// Export singleton instance
export const monitoring = new MonitoringService();

// Business Metrics Helpers
export const BusinessMetrics = {
  orderCreated: (tenantId: string, amount: number) => {
    monitoring.recordBusinessMetric({
      metric: 'orders.created',
      value: 1,
      dimensions: { tenantId },
    });
    
    monitoring.recordBusinessMetric({
      metric: 'revenue.generated',
      value: amount,
      dimensions: { tenantId },
    });
  },
  
  userSignup: (source: string) => {
    monitoring.recordBusinessMetric({
      metric: 'users.signup',
      value: 1,
      dimensions: { source },
    });
  },
  
  qrCodeScanned: (tenantId: string, roomId: string) => {
    monitoring.recordBusinessMetric({
      metric: 'qr.scanned',
      value: 1,
      dimensions: { tenantId, roomId },
    });
  },
  
  menuViewed: (tenantId: string) => {
    monitoring.recordBusinessMetric({
      metric: 'menu.viewed',
      value: 1,
      dimensions: { tenantId },
    });
  },
};

// Performance Monitoring Decorators
export function MonitorPerformance(operation: string) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;
    
    descriptor.value = async function (...args: any[]) {
      return monitoring.measureApiCall(
        `${target.constructor.name}.${propertyName}`,
        () => method.apply(this, args)
      );
    };
  };
}