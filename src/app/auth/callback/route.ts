import { createClient } from '@supabase/supabase-js';
import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  const requestUrl = new URL(request.url);
  const code = requestUrl.searchParams.get('code');
  const error = requestUrl.searchParams.get('error');
  const errorDescription = requestUrl.searchParams.get('error_description');
  const redirectParam = requestUrl.searchParams.get('redirect');

  // Default redirect URLs
  const defaultRedirect = '/admin/dashboard';
  const errorRedirect = '/login?error=oauth_error';

  // Handle OAuth errors
  if (error) {
    console.error('OAuth error:', error, errorDescription);
    return NextResponse.redirect(new URL(errorRedirect, requestUrl.origin));
  }

  if (!code) {
    console.error('No authorization code received');
    return NextResponse.redirect(new URL(errorRedirect, requestUrl.origin));
  }

  try {
    // Create Supabase client
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
    );

    // Exchange the code for a session
    const { data: { session }, error: sessionError } = await supabase.auth.exchangeCodeForSession(code);

    if (sessionError) {
      console.error('Session exchange error:', sessionError);
      return NextResponse.redirect(new URL(errorRedirect, requestUrl.origin));
    }

    if (!session) {
      console.error('No session created');
      return NextResponse.redirect(new URL(errorRedirect, requestUrl.origin));
    }

    // Get user information
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      console.error('User fetch error:', userError);
      return NextResponse.redirect(new URL(errorRedirect, requestUrl.origin));
    }

    // Check if this is a new user or existing user
    const isNewUser = user.created_at === user.last_sign_in_at;

    // For new users, we might want to create additional profile data
    if (isNewUser) {
      try {
        // Create user profile in our database if needed
        await createUserProfile(user, supabase);
      } catch (profileError) {
        console.error('Profile creation error:', profileError);
        // Don't fail the login for profile creation errors
      }
    }

    // Determine redirect URL
    let redirectUrl = redirectParam || defaultRedirect;
    
    // Validate redirect URL to prevent open redirects
    if (!isValidRedirectUrl(redirectUrl)) {
      redirectUrl = defaultRedirect;
    }

    // Add success parameter
    const finalUrl = new URL(redirectUrl, requestUrl.origin);
    finalUrl.searchParams.set('auth', 'success');
    
    if (isNewUser) {
      finalUrl.searchParams.set('new_user', 'true');
    }

    return NextResponse.redirect(finalUrl);

  } catch (error) {
    console.error('OAuth callback error:', error);
    return NextResponse.redirect(new URL(errorRedirect, requestUrl.origin));
  }
}

async function createUserProfile(user: any, supabase: any) {
  // Extract user information from Google OAuth
  const userMetadata = user.user_metadata || {};
  const email = user.email;
  const fullName = userMetadata.full_name || userMetadata.name || '';
  const firstName = userMetadata.given_name || '';
  const lastName = userMetadata.family_name || '';
  const avatarUrl = userMetadata.avatar_url || userMetadata.picture || '';

  // Check if user should be an admin (you can customize this logic)
  const isAdmin = await checkIfUserShouldBeAdmin(email);

  if (isAdmin) {
    // Create admin user profile
    const { error } = await supabase
      .from('AdminUsers')
      .upsert({
        auth_user_id: user.id,
        email: email,
        first_name: firstName,
        last_name: lastName,
        avatar_url: avatarUrl,
        role: 'TENANT_ADMIN', // Default role
        status: 'ACTIVE',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }, {
        onConflict: 'auth_user_id'
      });

    if (error) {
      console.error('Admin profile creation error:', error);
    }
  } else {
    // Create regular customer profile
    const { error } = await supabase
      .from('Users')
      .upsert({
        auth_user_id: user.id,
        email: email,
        name: fullName,
        avatar_url: avatarUrl,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }, {
        onConflict: 'auth_user_id'
      });

    if (error) {
      console.error('User profile creation error:', error);
    }
  }
}

async function checkIfUserShouldBeAdmin(email: string): Promise<boolean> {
  // Implement your logic to determine if a user should be an admin
  // This could be based on email domain, whitelist, etc.
  
  // Example: Check if email is in admin whitelist
  const adminEmails = process.env.ADMIN_EMAIL_WHITELIST?.split(',') || [];
  return adminEmails.includes(email);
}

function isValidRedirectUrl(url: string): boolean {
  try {
    // Allow relative URLs
    if (url.startsWith('/')) {
      return true;
    }
    
    // For absolute URLs, check if they're from the same origin
    const parsedUrl = new URL(url);
    const allowedOrigins = [
      process.env.NEXT_PUBLIC_APP_URL,
      'http://localhost:3000',
      'https://localhost:3000'
    ].filter(Boolean);
    
    return allowedOrigins.some(origin => parsedUrl.origin === origin);
  } catch {
    return false;
  }
}
