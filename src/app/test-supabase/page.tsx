'use client'

/**
 * Supabase Connection Test Page
 * Simple UI to test and verify Supabase integration
 */

import { useState } from 'react'
import { testSupabaseConnection, testSupabaseRealtime, type ConnectionTestResult } from '@/lib/supabase-connection-test'

export default function TestSupabasePage() {
  const [connectionResult, setConnectionResult] = useState<ConnectionTestResult | null>(null)
  const [realtimeResult, setRealtimeResult] = useState<ConnectionTestResult | null>(null)
  const [isTestingConnection, setIsTestingConnection] = useState(false)
  const [isTestingRealtime, setIsTestingRealtime] = useState(false)

  const handleTestConnection = async () => {
    setIsTestingConnection(true)
    try {
      const result = await testSupabaseConnection()
      setConnectionResult(result)
    } catch (error) {
      setConnectionResult({
        success: false,
        message: 'Test execution failed',
        details: { error: error instanceof Error ? error.message : 'Unknown error' },
        timestamp: new Date().toISOString()
      })
    } finally {
      setIsTestingConnection(false)
    }
  }

  const handleTestRealtime = async () => {
    setIsTestingRealtime(true)
    try {
      const result = await testSupabaseRealtime()
      setRealtimeResult(result)
    } catch (error) {
      setRealtimeResult({
        success: false,
        message: 'Realtime test execution failed',
        details: { error: error instanceof Error ? error.message : 'Unknown error' },
        timestamp: new Date().toISOString()
      })
    } finally {
      setIsTestingRealtime(false)
    }
  }

  const TestResultCard = ({ title, result, isLoading }: {
    title: string
    result: ConnectionTestResult | null
    isLoading: boolean
  }) => (
    <div className="border rounded-lg p-6 bg-white shadow-sm">
      <h3 className="text-lg font-semibold mb-4">{title}</h3>
      
      {isLoading && (
        <div className="flex items-center space-x-2 text-blue-600">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
          <span>Testing...</span>
        </div>
      )}

      {result && !isLoading && (
        <div className="space-y-3">
          <div className={`flex items-center space-x-2 ${result.success ? 'text-green-600' : 'text-red-600'}`}>
            <span className="text-xl">{result.success ? '✅' : '❌'}</span>
            <span className="font-medium">{result.message}</span>
          </div>
          
          <div className="text-sm text-gray-500">
            Tested at: {new Date(result.timestamp).toLocaleString()}
          </div>

          {result.details && (
            <div className="mt-4">
              <h4 className="font-medium text-gray-700 mb-2">Details:</h4>
              <pre className="bg-gray-50 p-3 rounded text-xs overflow-auto">
                {JSON.stringify(result.details, null, 2)}
              </pre>
            </div>
          )}
        </div>
      )}
    </div>
  )

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Supabase Integration Test
          </h1>
          <p className="text-gray-600">
            Verify that your BHEEMDINE application is properly connected to Supabase
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-6 mb-8">
          <TestResultCard
            title="Connection Test"
            result={connectionResult}
            isLoading={isTestingConnection}
          />
          
          <TestResultCard
            title="Realtime Test"
            result={realtimeResult}
            isLoading={isTestingRealtime}
          />
        </div>

        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <button
            onClick={handleTestConnection}
            disabled={isTestingConnection}
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {isTestingConnection ? 'Testing Connection...' : 'Test Connection'}
          </button>
          
          <button
            onClick={handleTestRealtime}
            disabled={isTestingRealtime}
            className="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {isTestingRealtime ? 'Testing Realtime...' : 'Test Realtime'}
          </button>
        </div>

        <div className="mt-12 bg-white rounded-lg p-6 shadow-sm">
          <h2 className="text-xl font-semibold mb-4">Environment Configuration</h2>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600">Supabase URL:</span>
              <span className="font-mono text-xs">
                {process.env.NEXT_PUBLIC_SUPABASE_URL ? 
                  `${process.env.NEXT_PUBLIC_SUPABASE_URL.substring(0, 30)}...` : 
                  '❌ Not configured'
                }
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Anon Key:</span>
              <span className="font-mono text-xs">
                {process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? 
                  `${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY.substring(0, 20)}...` : 
                  '❌ Not configured'
                }
              </span>
            </div>
          </div>
        </div>

        <div className="mt-8 text-center">
          <a
            href="/"
            className="text-blue-600 hover:text-blue-800 underline"
          >
            ← Back to Home
          </a>
        </div>
      </div>
    </div>
  )
}
