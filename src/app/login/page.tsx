/**
 * Tenant Admin Login Page - B2B SaaS Authentication
 */

import { Metadata } from 'next'
import TenantLoginForm from '@/components/auth/TenantLoginForm'

export const metadata: Metadata = {
  title: 'Admin Login - BHEEMDINE Restaurant Management',
  description: 'Sign in to your restaurant management dashboard. Secure access for business owners and administrators.',
  robots: 'index, follow',
  openGraph: {
    title: 'Admin Login - BHEEMDINE',
    description: 'Access your restaurant management dashboard with enterprise-grade security.',
    type: 'website',
  }
}

export default function LoginPage() {
  return <TenantLoginForm />
}
