// QR Code Generation Service with comprehensive customization options
// Handles single and bulk QR code generation with various export formats

import QRCode from 'qrcode';
import type {
  QRCodeOptions,
  QRCodeData,
  QRGenerationResult,
  BulkQRGenerationResult,
  ExportResult,
  ExportOptions,
  BulkLayoutOptions,
  Room
} from '@/types/qr-management';
import { DEFAULT_QR_OPTIONS } from '@/types/qr-management';

export class QRCodeGenerationService {
  
  // Dynamic import loaders for PDF libraries
  private async loadPDFLibraries() {
    try {
      const [jsPDF, html2canvas] = await Promise.all([
        import('jspdf'),
        import('html2canvas')
      ]);
      return {
        jsPDF: jsPDF.default,
        html2canvas: html2canvas.default
      };
    } catch (error) {
      console.error('Failed to load PDF libraries:', error);
      throw new Error('PDF generation libraries failed to load');
    }
  }
  
  // =====================================================
  // SINGLE QR CODE GENERATION
  // =====================================================
  
  /**
   * Generate a single QR code with custom options
   * Returns both dataURL and SVG representations for flexibility
   */
  async generateQRCode(
    room: Room,
    options: QRCodeOptions
  ): Promise<QRGenerationResult> {
    const startTime = performance.now();
    
    try {
      // Build the full URL for the QR code with proper validation
      const baseUrl = options.baseUrl || process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
      
      // Validate base URL
      if (!baseUrl || typeof baseUrl !== 'string' || baseUrl.trim() === '') {
        throw new Error('Invalid base URL provided');
      }
      
      // Additional validation for URL format
      try {
        new URL(baseUrl);
      } catch {
        throw new Error(`Invalid base URL format: ${baseUrl}`);
      }
      
      const menuUrl = new URL('/menu', baseUrl);
      
      // Add query parameters safely
      const params = new URLSearchParams();
      if (options.qrCode) params.set('qr', options.qrCode);
      if (room.id) params.set('room', room.id);
      if (room.tenantId) params.set('tenant', room.tenantId);
      
      const fullUrl = `${menuUrl.toString()}?${params.toString()}`;
      
      // Generate QR code configuration
      const qrConfig = {
        errorCorrectionLevel: options.errorCorrectionLevel,
        margin: options.margin,
        color: {
          dark: options.foregroundColor,
          light: options.backgroundColor,
        },
        width: options.size,
      };
      
      // Generate dataURL (PNG format)
      const dataUrl = await QRCode.toDataURL(fullUrl, qrConfig);
      
      // Generate SVG format for better scalability
      const svgData = await QRCode.toString(fullUrl, {
        ...qrConfig,
        type: 'svg',
        width: options.size,
      });
      
      // Apply logo overlay if requested
      let finalDataUrl = dataUrl;
      if (options.includelogo && options.logoUrl) {
        finalDataUrl = await this.applyLogoOverlay(dataUrl, options);
      }
      
      // Apply border if requested
      if (options.border) {
        finalDataUrl = await this.applyBorder(finalDataUrl, options);
      }
      
      const generationTime = performance.now() - startTime;
      
      // Build QR code data object
      const qrCodeData: QRCodeData = {
        id: `qr_${room.id}_${Date.now()}`,
        roomId: room.id,
        qrCode: options.qrCode,
        roomNumber: room.roomNumber,
        floor: room.floor,
        capacity: room.capacity,
        url: fullUrl,
        generatedAt: new Date().toISOString(),
        generatedBy: 'admin', // This would come from auth context
        options,
        exportCount: 0,
        dataUrl: finalDataUrl,
        svgData: svgData,
      };
      
      return {
        success: true,
        qrCode: qrCodeData,
        dataUrl: finalDataUrl,
        svgData,
        generationTime,
      };
      
    } catch (error) {
      console.error('QR generation error:', error);
      
      return {
        success: false,
        qrCode: {} as QRCodeData,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        generationTime: performance.now() - startTime,
      };
    }
  }
  
  /**
   * Regenerate QR code for an existing room
   * Useful when QR codes need to be refreshed for security
   */
  async regenerateQRCode(room: Room, options?: Partial<QRCodeOptions>): Promise<QRGenerationResult> {
    // Generate new unique QR code identifier
    const newQRCode = `qr_${room.id}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const fullOptions: QRCodeOptions = {
      ...DEFAULT_QR_OPTIONS, // Start with defaults
      ...(options || {}),
      qrCode: newQRCode,
    };
    
    return this.generateQRCode(room, fullOptions);
  }
  
  // =====================================================
  // BULK QR CODE GENERATION
  // =====================================================
  
  /**
   * Generate QR codes for multiple rooms in batch
   * Provides progress tracking and error handling for individual items
   */
  async bulkGenerateQRCodes(
    rooms: Room[],
    options: QRCodeOptions,
    onProgress?: (current: number, total: number) => void
  ): Promise<BulkQRGenerationResult> {
    const startTime = performance.now();
    const results: QRGenerationResult[] = [];
    const failed: Array<{ roomId: string; error: string }> = [];
    
    try {
      for (let i = 0; i < rooms.length; i++) {
        const room = rooms[i];
        
        try {
          // Generate unique QR code for each room
          const roomOptions: QRCodeOptions = {
            ...options,
            qrCode: `qr_${room.id}_${Date.now()}_${i}`,
          };
          
          const result = await this.generateQRCode(room, roomOptions);
          results.push(result);
          
          // Report progress
          if (onProgress) {
            onProgress(i + 1, rooms.length);
          }
          
          // Small delay to prevent overwhelming the system
          if (i < rooms.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 10));
          }
          
        } catch (error) {
          failed.push({
            roomId: room.id,
            error: error instanceof Error ? error.message : 'Unknown error',
          });
        }
      }
      
      const processingTime = performance.now() - startTime;
      const successCount = results.filter(r => r.success).length;
      const failureCount = failed.length;
      
      return {
        success: failureCount === 0,
        results,
        failed,
        totalProcessed: rooms.length,
        successCount,
        failureCount,
        processingTime,
      };
      
    } catch (error) {
      return {
        success: false,
        results: [],
        failed: rooms.map(room => ({
          roomId: room.id,
          error: error instanceof Error ? error.message : 'Bulk operation failed',
        })),
        totalProcessed: rooms.length,
        successCount: 0,
        failureCount: rooms.length,
        processingTime: performance.now() - startTime,
      };
    }
  }
  
  // =====================================================
  // EXPORT FUNCTIONALITY
  // =====================================================
  
  /**
   * Export single QR code in various formats
   */
  async exportQRCode(
    qrCodeData: QRCodeData,
    exportOptions: ExportOptions
  ): Promise<ExportResult> {
    try {
      const { format, quality, size } = exportOptions;
      
      switch (format) {
        case 'png':
          return await this.exportAsPNG(qrCodeData, exportOptions);
          
        case 'svg':
          return await this.exportAsSVG(qrCodeData, exportOptions);
          
        case 'pdf':
          return await this.exportAsPDF([qrCodeData], exportOptions);
          
        default:
          throw new Error(`Unsupported export format: ${format}`);
      }
      
    } catch (error) {
      return {
        success: false,
        format: exportOptions.format,
        size: 0,
        filename: '',
        error: error instanceof Error ? error.message : 'Export failed',
      };
    }
  }
  
  /**
   * Export multiple QR codes as PDF with custom layout
   */
  async bulkExportQRCodes(
    qrCodes: QRCodeData[],
    exportOptions: ExportOptions
  ): Promise<ExportResult> {
    try {
      if (exportOptions.format === 'pdf') {
        return await this.exportAsPDF(qrCodes, exportOptions);
      } else {
        // For non-PDF formats, create a ZIP file
        return await this.exportAsZip(qrCodes, exportOptions);
      }
      
    } catch (error) {
      return {
        success: false,
        format: exportOptions.format,
        size: 0,
        filename: '',
        error: error instanceof Error ? error.message : 'Bulk export failed',
      };
    }
  }
  
  // =====================================================
  // PRIVATE HELPER METHODS
  // =====================================================
  
  /**
   * Apply logo overlay to QR code
   */
  private async applyLogoOverlay(
    qrDataUrl: string,
    options: QRCodeOptions
  ): Promise<string> {
    return new Promise((resolve, reject) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      if (!ctx) {
        reject(new Error('Canvas context not available'));
        return;
      }
      
      const qrImage = new Image();
      qrImage.onload = () => {
        canvas.width = qrImage.width;
        canvas.height = qrImage.height;
        
        // Draw QR code
        ctx.drawImage(qrImage, 0, 0);
        
        // Load and draw logo
        const logoImage = new Image();
        logoImage.onload = () => {
          const logoSize = (options.logoSize || 20) / 100 * qrImage.width;
          const logoX = (qrImage.width - logoSize) / 2;
          const logoY = (qrImage.height - logoSize) / 2;
          
          // Add white background for logo
          ctx.fillStyle = 'white';
          ctx.fillRect(logoX - 4, logoY - 4, logoSize + 8, logoSize + 8);
          
          // Draw logo
          ctx.drawImage(logoImage, logoX, logoY, logoSize, logoSize);
          
          resolve(canvas.toDataURL('image/png'));
        };
        
        logoImage.onerror = () => reject(new Error('Failed to load logo'));
        logoImage.src = options.logoUrl!;
      };
      
      qrImage.onerror = () => reject(new Error('Failed to load QR code'));
      qrImage.src = qrDataUrl;
    });
  }
  
  /**
   * Apply decorative border to QR code
   */
  private async applyBorder(
    qrDataUrl: string,
    options: QRCodeOptions
  ): Promise<string> {
    return new Promise((resolve, reject) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      if (!ctx) {
        reject(new Error('Canvas context not available'));
        return;
      }
      
      const qrImage = new Image();
      qrImage.onload = () => {
        const borderWidth = options.borderWidth || 2;
        canvas.width = qrImage.width + (borderWidth * 2);
        canvas.height = qrImage.height + (borderWidth * 2);
        
        // Draw border
        ctx.fillStyle = options.borderColor || '#000000';
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        
        // Draw QR code on top
        ctx.drawImage(qrImage, borderWidth, borderWidth);
        
        resolve(canvas.toDataURL('image/png'));
      };
      
      qrImage.onerror = () => reject(new Error('Failed to load QR code for border'));
      qrImage.src = qrDataUrl;
    });
  }
  
  /**
   * Export as PNG format
   */
  private async exportAsPNG(
    qrCodeData: QRCodeData,
    options: ExportOptions
  ): Promise<ExportResult> {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    if (!ctx) {
      throw new Error('Canvas context not available');
    }
    
    const image = new Image();
    await new Promise((resolve, reject) => {
      image.onload = resolve;
      image.onerror = reject;
      image.src = qrCodeData.dataUrl!;
    });
    
    // Resize if needed
    const targetSize = options.size || 256;
    canvas.width = targetSize;
    canvas.height = targetSize;
    
    ctx.drawImage(image, 0, 0, targetSize, targetSize);
    
    // Convert to blob
    const blob = await new Promise<Blob>((resolve, reject) => {
      canvas.toBlob((blob) => {
        if (blob) resolve(blob);
        else reject(new Error('Failed to create blob'));
      }, 'image/png', options.quality / 100);
    });
    
    const filename = `qr_${qrCodeData.roomNumber}_${Date.now()}.png`;
    
    return {
      success: true,
      format: 'png',
      size: blob.size,
      blob,
      filename,
      downloadUrl: URL.createObjectURL(blob),
    };
  }
  
  /**
   * Export as SVG format
   */
  private async exportAsSVG(
    qrCodeData: QRCodeData,
    options: ExportOptions
  ): Promise<ExportResult> {
    const svgContent = qrCodeData.svgData;
    if (!svgContent) {
      throw new Error('SVG data not available');
    }
    
    const blob = new Blob([svgContent], { type: 'image/svg+xml' });
    const filename = `qr_${qrCodeData.roomNumber}_${Date.now()}.svg`;
    
    return {
      success: true,
      format: 'svg',
      size: blob.size,
      blob,
      filename,
      downloadUrl: URL.createObjectURL(blob),
    };
  }
  
  /**
   * Export as PDF with custom layout
   */
  private async exportAsPDF(
    qrCodes: QRCodeData[],
    options: ExportOptions
  ): Promise<ExportResult> {
    const { jsPDF } = await this.loadPDFLibraries();
    const layout = options.pdfOptions?.layout || this.getDefaultLayout();
    const pdf = new jsPDF({
      orientation: layout.orientation,
      unit: 'mm',
      format: layout.pageSize.toLowerCase() as any,
    });
    
    const pageWidth = pdf.internal.pageSize.getWidth();
    const pageHeight = pdf.internal.pageSize.getHeight();
    
    // Calculate grid dimensions
    const contentWidth = pageWidth - (layout.margin * 2);
    const contentHeight = pageHeight - (layout.margin * 2);
    const cellWidth = contentWidth / layout.columns;
    const cellHeight = contentHeight / layout.rows;
    const qrSize = Math.min(cellWidth, cellHeight) - layout.spacing;
    
    let currentPage = 0;
    let currentRow = 0;
    let currentCol = 0;
    
    for (let i = 0; i < qrCodes.length; i++) {
      const qrCode = qrCodes[i];
      
      // Add new page if needed
      if (i > 0 && currentRow === 0 && currentCol === 0) {
        pdf.addPage();
        currentPage++;
      }
      
      // Calculate position
      const x = layout.margin + (currentCol * cellWidth) + (cellWidth - qrSize) / 2;
      const y = layout.margin + (currentRow * cellHeight) + (cellHeight - qrSize) / 2;
      
      // Add QR code image
      if (qrCode.dataUrl) {
        pdf.addImage(qrCode.dataUrl, 'PNG', x, y, qrSize, qrSize);
      }
      
      // Add room information if enabled
      if (layout.includeRoomNumber || layout.includeFloor) {
        const textY = y + qrSize + 5;
        pdf.setFontSize(layout.fontSize);
        
        let text = '';
        if (layout.includeRoomNumber) {
          text += `Room ${qrCode.roomNumber}`;
        }
        if (layout.includeFloor && qrCode.floor) {
          text += layout.includeRoomNumber ? ` - Floor ${qrCode.floor}` : `Floor ${qrCode.floor}`;
        }
        
        const textWidth = pdf.getTextWidth(text);
        const textX = x + (qrSize - textWidth) / 2;
        pdf.text(text, textX, textY);
      }
      
      // Update grid position
      currentCol++;
      if (currentCol >= layout.columns) {
        currentCol = 0;
        currentRow++;
        if (currentRow >= layout.rows) {
          currentRow = 0;
        }
      }
    }
    
    // Add headers and footers if enabled
    if (layout.title) {
      pdf.setPage(1);
      pdf.setFontSize(layout.titleFontSize);
      pdf.text(layout.title, pageWidth / 2, layout.margin / 2, { align: 'center' });
    }
    
    const pdfBlob = pdf.output('blob');
    const filename = `qr_codes_bulk_${Date.now()}.pdf`;
    
    return {
      success: true,
      format: 'pdf',
      size: pdfBlob.size,
      blob: pdfBlob,
      filename,
      downloadUrl: URL.createObjectURL(pdfBlob),
    };
  }
  
  /**
   * Export multiple files as ZIP
   */
  private async exportAsZip(
    qrCodes: QRCodeData[],
    options: ExportOptions
  ): Promise<ExportResult> {
    // This would require a ZIP library like JSZip
    // For now, we'll return an error suggesting PDF export for multiple files
    throw new Error('ZIP export not implemented. Please use PDF format for bulk export.');
  }
  
  /**
   * Get default layout options
   */
  private getDefaultLayout(): BulkLayoutOptions {
    return {
      pageSize: 'A4',
      orientation: 'portrait',
      margin: 20,
      columns: 3,
      rows: 4,
      spacing: 10,
      includeRoomNumber: true,
      includeFloor: true,
      includeQRCodeText: true,
      includeInstructions: true,
      fontSize: 12,
      fontFamily: 'Arial, sans-serif',
      titleFontSize: 16,
      includeGenerationDate: true,
      includeTenantInfo: true,
    };
  }
  
  // =====================================================
  // UTILITY METHODS
  // =====================================================
  
  /**
   * Validate QR code by scanning it
   */
  async validateQRCode(dataUrl: string): Promise<{ isValid: boolean; url?: string; error?: string }> {
    try {
      // This would require a QR code reader library
      // For now, we'll return a simple validation
      return {
        isValid: true,
        url: 'Validation would require QR reader library',
      };
    } catch (error) {
      return {
        isValid: false,
        error: error instanceof Error ? error.message : 'Validation failed',
      };
    }
  }
  
  /**
   * Get optimal QR code size based on content length
   */
  getOptimalSize(content: string): number {
    if (content.length < 50) return 128;
    if (content.length < 100) return 256;
    if (content.length < 200) return 384;
    return 512;
  }
  
  /**
   * Preview QR code options before generation
   */
  async previewOptions(options: QRCodeOptions): Promise<string> {
    const baseUrl = options.baseUrl || 'http://localhost:3000';
    
    // Validate base URL
    if (!baseUrl || typeof baseUrl !== 'string' || baseUrl.trim() === '') {
      throw new Error('Invalid base URL provided for preview');
    }
    
    // Additional validation for URL format
    try {
      new URL(baseUrl);
    } catch {
      throw new Error(`Invalid base URL format for preview: ${baseUrl}`);
    }
    
    const menuUrl = new URL('/menu', baseUrl);
    const params = new URLSearchParams();
    params.set('qr', 'sample_preview');
    const sampleUrl = `${menuUrl.toString()}?${params.toString()}`;
    
    try {
      return await QRCode.toDataURL(sampleUrl, {
        errorCorrectionLevel: options.errorCorrectionLevel,
        margin: options.margin,
        color: {
          dark: options.foregroundColor,
          light: options.backgroundColor,
        },
        width: options.size,
      });
    } catch (error) {
      throw new Error(`Preview generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}