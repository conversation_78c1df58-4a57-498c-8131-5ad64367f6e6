/**
 * Enhanced Supabase Middleware for B2B Multi-Tenant SaaS
 * Handles authentication, tenant validation, and route protection
 */

import { createServerClient } from '@supabase/ssr'
import { NextRequest, NextResponse } from 'next/server'
import { Database } from '@/types/database'

/**
 * Configuration for different route types and their protection levels
 */
const ROUTE_CONFIG = {
  // Public routes - no authentication required
  public: [
    '/',
    '/about',
    '/pricing',
    '/contact',
    '/legal/privacy',
    '/legal/terms',
    '/api/health',
  ],
  
  // Auth routes - redirect if already authenticated
  auth: [
    '/login',
    '/signup',
    '/auth/callback',
    '/auth/reset-password',
    '/auth/verify',
  ],
  
  // Admin routes - require admin authentication and tenant context
  admin: [
    '/admin',
    '/admin/dashboard',
    '/admin/menu',
    '/admin/orders',
    '/admin/qr',
    '/admin/staff',
    '/admin/settings',
    '/admin/analytics',
  ],
  
  // API routes that require authentication
  protectedApi: [
    '/api/admin',
    '/api/menu/admin',
    '/api/orders/admin',
    '/api/tenants',
    '/api/staff',
  ],
  
  // Menu routes - require tenant context but not admin auth
  menu: [
    '/menu',
  ],
}

/**
 * Enhanced middleware for B2B tenant authentication and authorization
 */
export async function updateSession(request: NextRequest) {
  let response = NextResponse.next({
    request: {
      headers: request.headers,
    },
  })

  const supabase = createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return request.cookies.getAll()
        },
        setAll(cookiesToSet) {
          cookiesToSet.forEach(({ name, value, options }) => {
            request.cookies.set(name, value)
            response.cookies.set(name, value, options)
          })
        },
      },
    }
  )

  // Get current session
  const { data: { session }, error: sessionError } = await supabase.auth.getSession()
  
  const pathname = request.nextUrl.pathname
  const searchParams = request.nextUrl.searchParams
  const tenantSlug = searchParams.get('tenant') || extractTenantFromPath(pathname)

  // Determine route type
  const routeType = getRouteType(pathname)

  try {
    // Handle different route types
    switch (routeType) {
      case 'public':
        return handlePublicRoute(request, response, session)
        
      case 'auth':
        return handleAuthRoute(request, response, session)
        
      case 'admin':
        return await handleAdminRoute(request, response, session, supabase, tenantSlug)
        
      case 'protectedApi':
        return await handleProtectedApiRoute(request, response, session, supabase, tenantSlug)
        
      case 'menu':
        return await handleMenuRoute(request, response, session, supabase, tenantSlug)
        
      default:
        return response
    }
  } catch (error) {
    console.error('Middleware error:', error)
    
    // For API routes, return JSON error
    if (pathname.startsWith('/api/')) {
      return NextResponse.json(
        { error: 'Authentication service unavailable' },
        { status: 503 }
      )
    }
    
    // For web routes, redirect to error page
    const errorUrl = new URL('/error', request.url)
    errorUrl.searchParams.set('message', 'Service temporarily unavailable')
    return NextResponse.redirect(errorUrl)
  }
}

/**
 * Handle public routes - no authentication required
 */
function handlePublicRoute(
  request: NextRequest, 
  response: NextResponse, 
  session: any
): NextResponse {
  // Add security headers
  response.headers.set('X-Frame-Options', 'DENY')
  response.headers.set('X-Content-Type-Options', 'nosniff')
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin')
  
  return response
}

/**
 * Handle auth routes - redirect if already authenticated
 */
function handleAuthRoute(
  request: NextRequest, 
  response: NextResponse, 
  session: any
): NextResponse {
  const pathname = request.nextUrl.pathname
  
  // If user is already authenticated, redirect to admin dashboard
  if (session) {
    const tenantSlug = request.nextUrl.searchParams.get('tenant')
    const redirectUrl = tenantSlug 
      ? `/admin/dashboard?tenant=${tenantSlug}`
      : '/admin/dashboard'
    
    return NextResponse.redirect(new URL(redirectUrl, request.url))
  }
  
  // Allow access to auth routes for unauthenticated users
  return response
}

/**
 * Handle admin routes - require admin authentication and tenant validation
 */
async function handleAdminRoute(
  request: NextRequest,
  response: NextResponse,
  session: any,
  supabase: any,
  tenantSlug: string | null
): Promise<NextResponse> {
  // Check if user is authenticated
  if (!session) {
    const loginUrl = new URL('/login', request.url)
    if (tenantSlug) {
      loginUrl.searchParams.set('tenant', tenantSlug)
    }
    loginUrl.searchParams.set('redirect', request.nextUrl.pathname + request.nextUrl.search)
    return NextResponse.redirect(loginUrl)
  }

  try {
    // Get admin user profile
    const { data: adminUser, error } = await supabase
      .from('AdminUser')
      .select(`
        *,
        tenant:Tenant(*)
      `)
      .eq('authUserId', session.user.id)
      .eq('status', 'ACTIVE')
      .single()

    if (error || !adminUser) {
      return NextResponse.redirect(new URL('/auth/unauthorized', request.url))
    }

    // Check if tenant is active
    if (!adminUser.tenant?.isActive) {
      const suspendedUrl = new URL('/auth/tenant-suspended', request.url)
      return NextResponse.redirect(suspendedUrl)
    }

    // Validate tenant access if tenant slug is specified
    if (tenantSlug) {
      const hasAccess = await validateTenantAccess(supabase, session.user.id, tenantSlug)
      if (!hasAccess) {
        return NextResponse.redirect(new URL('/auth/unauthorized', request.url))
      }
    }

    // Add tenant context to response headers for use in components
    if (adminUser.tenant) {
      response.headers.set('X-Tenant-ID', adminUser.tenant.id)
      response.headers.set('X-Tenant-Slug', adminUser.tenant.slug)
      response.headers.set('X-User-Role', adminUser.role)
    }

    // Rate limiting for admin actions
    await logUserActivity(supabase, session.user.id, 'page_view', request.nextUrl.pathname)

    return response
  } catch (error) {
    console.error('Admin route validation error:', error)
    return NextResponse.redirect(new URL('/auth/error', request.url))
  }
}

/**
 * Handle protected API routes - require authentication and tenant validation
 */
async function handleProtectedApiRoute(
  request: NextRequest,
  response: NextResponse,
  session: any,
  supabase: any,
  tenantSlug: string | null
): Promise<NextResponse> {
  // Check authentication
  if (!session) {
    return NextResponse.json(
      { error: 'Authentication required' },
      { status: 401 }
    )
  }

  try {
    // Get admin user profile for API access
    const { data: adminUser, error } = await supabase
      .from('AdminUser')
      .select(`
        *,
        tenant:Tenant(*)
      `)
      .eq('authUserId', session.user.id)
      .eq('status', 'ACTIVE')
      .single()

    if (error || !adminUser) {
      return NextResponse.json(
        { error: 'Access denied' },
        { status: 403 }
      )
    }

    // Validate tenant access for tenant-specific API routes
    if (tenantSlug) {
      const hasAccess = await validateTenantAccess(supabase, session.user.id, tenantSlug)
      if (!hasAccess) {
        return NextResponse.json(
          { error: 'Tenant access denied' },
          { status: 403 }
        )
      }
    }

    // Rate limiting for API requests
    const rateLimitResult = await checkApiRateLimit(supabase, session.user.id, request.method)
    if (!rateLimitResult.allowed) {
      return NextResponse.json(
        { 
          error: 'Rate limit exceeded', 
          retryAfter: rateLimitResult.retryAfter 
        },
        { 
          status: 429,
          headers: {
            'Retry-After': rateLimitResult.retryAfter?.toString() || '60'
          }
        }
      )
    }

    // Add tenant context to request headers
    if (adminUser.tenant) {
      response.headers.set('X-Tenant-ID', adminUser.tenant.id)
      response.headers.set('X-Tenant-Slug', adminUser.tenant.slug)
      response.headers.set('X-User-Role', adminUser.role)
      response.headers.set('X-User-Permissions', JSON.stringify(adminUser.permissions || []))
    }

    return response
  } catch (error) {
    console.error('API route validation error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

/**
 * Handle menu routes - require tenant context but not admin auth
 */
async function handleMenuRoute(
  request: NextRequest,
  response: NextResponse,
  session: any,
  supabase: any,
  tenantSlug: string | null
): Promise<NextResponse> {
  const qrCode = request.nextUrl.searchParams.get('qr')
  
  // If no tenant context and no QR code, redirect to home
  if (!tenantSlug && !qrCode) {
    return NextResponse.redirect(new URL('/', request.url))
  }

  try {
    let tenant = null

    // Get tenant from QR code if provided
    if (qrCode) {
      const { data: room } = await supabase
        .from('Room')
        .select(`
          tenant:Tenant(*)
        `)
        .eq('qrCode', qrCode)
        .single()
      
      tenant = room?.tenant
    } else if (tenantSlug) {
      // Get tenant from slug
      const { data: tenantData } = await supabase
        .from('Tenant')
        .select('*')
        .eq('slug', tenantSlug)
        .eq('isActive', true)
        .single()
      
      tenant = tenantData
    }

    // If tenant not found or inactive, show error
    if (!tenant || !tenant.isActive) {
      const errorUrl = new URL('/menu/error', request.url)
      errorUrl.searchParams.set('message', 'Restaurant not found or temporarily unavailable')
      return NextResponse.redirect(errorUrl)
    }

    // Add tenant context to response headers
    response.headers.set('X-Tenant-ID', tenant.id)
    response.headers.set('X-Tenant-Slug', tenant.slug)
    response.headers.set('X-Tenant-Name', tenant.name)
    
    if (qrCode) {
      response.headers.set('X-QR-Code', qrCode)
    }

    return response
  } catch (error) {
    console.error('Menu route validation error:', error)
    const errorUrl = new URL('/menu/error', request.url)
    errorUrl.searchParams.set('message', 'Service temporarily unavailable')
    return NextResponse.redirect(errorUrl)
  }
}

/**
 * Utility functions
 */

function getRouteType(pathname: string): string {
  if (ROUTE_CONFIG.public.some(route => pathname === route || pathname.startsWith(route))) {
    return 'public'
  }
  
  if (ROUTE_CONFIG.auth.some(route => pathname.startsWith(route))) {
    return 'auth'
  }
  
  if (ROUTE_CONFIG.admin.some(route => pathname.startsWith(route))) {
    return 'admin'
  }
  
  if (ROUTE_CONFIG.protectedApi.some(route => pathname.startsWith(route))) {
    return 'protectedApi'
  }
  
  if (ROUTE_CONFIG.menu.some(route => pathname.startsWith(route))) {
    return 'menu'
  }
  
  return 'unknown'
}

function extractTenantFromPath(pathname: string): string | null {
  // Extract tenant from URL patterns like /t/[tenant-slug]/...
  const match = pathname.match(/^\/t\/([^\/]+)/)
  return match ? match[1] : null
}

async function validateTenantAccess(
  supabase: any, 
  userId: string, 
  tenantSlug: string
): Promise<boolean> {
  try {
    const { data, error } = await supabase
      .from('AdminUser')
      .select(`
        tenant:Tenant!inner(slug, isActive)
      `)
      .eq('authUserId', userId)
      .eq('status', 'ACTIVE')
      .eq('tenant.slug', tenantSlug)
      .eq('tenant.isActive', true)

    return !error && data && data.length > 0
  } catch (error) {
    console.error('Tenant access validation error:', error)
    return false
  }
}

async function logUserActivity(
  supabase: any,
  userId: string,
  action: string,
  resource: string
): Promise<void> {
  try {
    await supabase
      .from('AuditLog')
      .insert({
        userId,
        action,
        resource,
        metadata: {
          timestamp: new Date().toISOString(),
          source: 'middleware'
        }
      })
  } catch (error) {
    console.error('Failed to log user activity:', error)
  }
}

async function checkApiRateLimit(
  supabase: any,
  userId: string,
  method: string
): Promise<{ allowed: boolean; retryAfter?: number }> {
  try {
    const windowStart = new Date(Date.now() - 15 * 60 * 1000) // 15 minutes
    const maxRequests = method === 'GET' ? 100 : 50 // Different limits for read vs write

    const { data: attempts, error } = await supabase
      .from('AuditLog')
      .select('id')
      .eq('userId', userId)
      .eq('action', 'api_request')
      .gte('createdAt', windowStart.toISOString())

    if (error) {
      return { allowed: true } // Allow on error
    }

    const requestCount = attempts?.length || 0
    const allowed = requestCount < maxRequests

    return {
      allowed,
      retryAfter: allowed ? undefined : 900 // 15 minutes
    }
  } catch (error) {
    console.error('Rate limit check error:', error)
    return { allowed: true } // Allow on error
  }
}

/**
 * Middleware configuration
 */
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
}