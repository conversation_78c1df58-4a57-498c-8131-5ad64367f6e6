/**
 * Supabase Server-side Configuration for B2B Multi-Tenant SaaS
 * Enhanced with security features and tenant validation
 */

import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'
import { Database } from '@/types/database'
import { NextRequest, NextResponse } from 'next/server'

/**
 * Create server-side Supabase client with enhanced security
 */
export function createClient() {
  const cookieStore = cookies()

  return createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return cookieStore.getAll()
        },
        setAll(cookiesToSet) {
          try {
            cookiesToSet.forEach(({ name, value, options }) =>
              cookieStore.set(name, value, options)
            )
          } catch {
            // The `setAll` method was called from a Server Component.
            // This can be ignored if you have middleware refreshing
            // user sessions.
          }
        },
      },
      auth: {
        autoRefreshToken: true,
        persistSession: true,
        detectSessionInUrl: false, // Server-side doesn't need URL detection
        flowType: 'pkce',
      },
      // Enhanced security headers
      global: {
        headers: {
          'X-Client-Info': 'bheemdine-server@1.0.0',
        },
      },
    }
  )
}

/**
 * Create server-side client with service role for admin operations
 */
export function createServiceClient() {
  return createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      cookies: {
        getAll() {
          return []
        },
        setAll() {
          // No-op for service client
        },
      },
      auth: {
        autoRefreshToken: false,
        persistSession: false,
      },
      global: {
        headers: {
          'X-Client-Info': 'bheemdine-service@1.0.0',
        },
      },
    }
  )
}

/**
 * Enhanced server authentication utilities for B2B tenant management
 */
export class BHEEMDINEServerAuth {
  private client = createClient()
  private serviceClient = createServiceClient()

  /**
   * Get current authenticated user with tenant information
   */
  async getUser() {
    try {
      const { data: { user }, error } = await this.client.auth.getUser()
      
      if (error || !user) {
        return { user: null, adminUser: null, tenant: null, error }
      }

      // Get admin user profile with tenant information
      const { data: adminUser, error: profileError } = await this.client
        .from('AdminUser')
        .select(`
          *,
          tenant:Tenant(*)
        `)
        .eq('authUserId', user.id)
        .eq('status', 'ACTIVE')
        .single()

      if (profileError) {
        return { user, adminUser: null, tenant: null, error: profileError }
      }

      return { 
        user, 
        adminUser, 
        tenant: adminUser?.tenant, 
        error: null 
      }
    } catch (error) {
      return { user: null, adminUser: null, tenant: null, error }
    }
  }

  /**
   * Validate user has access to specific tenant
   */
  async validateTenantAccess(tenantSlug: string) {
    try {
      const { user, adminUser, error } = await this.getUser()
      
      if (error || !user || !adminUser) {
        return { hasAccess: false, error: error || 'User not authenticated' }
      }

      // Check if user has access to the requested tenant
      const { data: accessibleTenants, error: tenantsError } = await this.client
        .from('AdminUser')
        .select(`
          tenant:Tenant!inner(
            id,
            slug,
            isActive
          )
        `)
        .eq('authUserId', user.id)
        .eq('status', 'ACTIVE')
        .eq('tenant.slug', tenantSlug)
        .eq('tenant.isActive', true)

      if (tenantsError) {
        return { hasAccess: false, error: tenantsError }
      }

      const hasAccess = accessibleTenants && accessibleTenants.length > 0

      return { hasAccess, error: null }
    } catch (error) {
      return { hasAccess: false, error }
    }
  }

  /**
   * Get tenant by slug with security validation
   */
  async getTenantBySlug(tenantSlug: string) {
    try {
      const { data: tenant, error } = await this.client
        .from('Tenant')
        .select('*')
        .eq('slug', tenantSlug)
        .eq('isActive', true)
        .single()

      if (error) {
        return { tenant: null, error }
      }

      return { tenant, error: null }
    } catch (error) {
      return { tenant: null, error }
    }
  }

  /**
   * Create new tenant (service role required)
   */
  async createTenant(tenantData: any) {
    try {
      const { data: tenant, error } = await this.serviceClient
        .from('Tenant')
        .insert(tenantData)
        .select()
        .single()

      if (error) {
        return { tenant: null, error }
      }

      return { tenant, error: null }
    } catch (error) {
      return { tenant: null, error }
    }
  }

  /**
   * Create admin user for tenant (service role required)
   */
  async createAdminUser(adminUserData: any) {
    try {
      const { data: adminUser, error } = await this.serviceClient
        .from('AdminUser')
        .insert(adminUserData)
        .select()
        .single()

      if (error) {
        return { adminUser: null, error }
      }

      return { adminUser, error: null }
    } catch (error) {
      return { adminUser: null, error }
    }
  }

  /**
   * Validate admin permissions for specific action
   */
  async validatePermissions(requiredPermissions: string[], tenantSlug?: string) {
    try {
      const { user, adminUser, error } = await this.getUser()
      
      if (error || !user || !adminUser) {
        return { hasPermission: false, error: error || 'User not authenticated' }
      }

      // Validate tenant access if specified
      if (tenantSlug) {
        const { hasAccess, error: accessError } = await this.validateTenantAccess(tenantSlug)
        if (!hasAccess) {
          return { hasPermission: false, error: accessError || 'Tenant access denied' }
        }
      }

      // Check if user is owner (has all permissions)
      if (adminUser.role === 'OWNER') {
        return { hasPermission: true, error: null }
      }

      // Check specific permissions
      const userPermissions = adminUser.permissions || []
      const hasRequiredPermissions = requiredPermissions.every(permission => 
        userPermissions.includes(permission)
      )

      return { hasPermission: hasRequiredPermissions, error: null }
    } catch (error) {
      return { hasPermission: false, error }
    }
  }

  /**
   * Log security event (audit trail)
   */
  async logSecurityEvent(eventData: {
    action: string
    resource: string
    resourceId?: string
    metadata?: Record<string, any>
    ipAddress?: string
    userAgent?: string
  }) {
    try {
      const { user } = await this.getUser()
      
      const auditData = {
        userId: user?.id,
        tenantId: null, // Will be filled by trigger if user has tenant context
        action: eventData.action,
        resource: eventData.resource,
        resourceId: eventData.resourceId,
        ipAddress: eventData.ipAddress,
        userAgent: eventData.userAgent,
        metadata: eventData.metadata,
      }

      await this.serviceClient
        .from('AuditLog')
        .insert(auditData)

    } catch (error) {
      console.error('Failed to log security event:', error)
    }
  }

  /**
   * Check rate limiting for user actions
   */
  async checkRateLimit(action: string, windowMinutes: number = 15, maxAttempts: number = 10) {
    try {
      const { user } = await this.getUser()
      
      if (!user) {
        return { allowed: false, error: 'User not authenticated' }
      }

      const windowStart = new Date(Date.now() - windowMinutes * 60000)

      const { data: attempts, error } = await this.client
        .from('AuditLog')
        .select('id')
        .eq('userId', user.id)
        .eq('action', action)
        .gte('createdAt', windowStart.toISOString())

      if (error) {
        return { allowed: true, error: null } // Allow on error
      }

      const attemptCount = attempts?.length || 0
      const allowed = attemptCount < maxAttempts

      return { 
        allowed, 
        error: allowed ? null : `Rate limit exceeded for ${action}`,
        remainingAttempts: Math.max(0, maxAttempts - attemptCount)
      }
    } catch (error) {
      return { allowed: true, error: null } // Allow on error
    }
  }
}

/**
 * Middleware helper for route protection
 */
export async function createMiddlewareClient(request: NextRequest) {
  let response = NextResponse.next({
    request: {
      headers: request.headers,
    },
  })

  const supabase = createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return request.cookies.getAll()
        },
        setAll(cookiesToSet) {
          cookiesToSet.forEach(({ name, value, options }) => {
            request.cookies.set(name, value)
            response.cookies.set(name, value, options)
          })
        },
      },
      auth: {
        autoRefreshToken: true,
        persistSession: true,
        detectSessionInUrl: false,
        flowType: 'pkce',
      },
    }
  )

  // Refresh session if needed
  const { data: { session }, error } = await supabase.auth.getSession()

  return { supabase, response, session, error }
}

/**
 * Enhanced server auth instance
 */
export const serverAuth = new BHEEMDINEServerAuth()

/**
 * Database helpers with tenant isolation for server-side operations
 */
export class TenantAwareServerClient {
  private client = createClient()
  private serviceClient = createServiceClient()

  /**
   * Query with automatic tenant filtering and user validation
   */
  async query(table: string, options: any = {}) {
    try {
      // Get current user and tenant context
      const { user, adminUser, tenant, error } = await serverAuth.getUser()
      
      if (error || !user || !adminUser) {
        return { data: null, error: error || 'Authentication required' }
      }

      let query = this.client.from(table)

      // Add tenant filtering if not disabled
      if (options.tenantFiltering !== false && tenant) {
        query = query.eq('tenantId', tenant.id)
      }

      // Apply other filters
      if (options.select) query = query.select(options.select)
      if (options.eq) {
        Object.entries(options.eq).forEach(([key, value]) => {
          query = query.eq(key, value)
        })
      }
      if (options.order) query = query.order(options.order.column, options.order.options)
      if (options.limit) query = query.limit(options.limit)

      return await query
    } catch (error) {
      return { data: null, error }
    }
  }

  /**
   * Insert with automatic tenant assignment and user validation
   */
  async insert(table: string, data: any, options: any = {}) {
    try {
      const { user, adminUser, tenant, error } = await serverAuth.getUser()
      
      if (error || !user || !adminUser) {
        return { data: null, error: error || 'Authentication required' }
      }

      let insertData = { ...data }

      // Add tenant ID if not present and tenant context is available
      if (options.autoTenant !== false && tenant && !insertData.tenantId) {
        insertData.tenantId = tenant.id
      }

      // Add user context if specified
      if (options.addUserContext) {
        insertData.createdBy = user.id
        insertData.updatedBy = user.id
      }

      return await this.client.from(table).insert(insertData)
    } catch (error) {
      return { data: null, error }
    }
  }

  /**
   * Update with tenant validation and user context
   */
  async update(table: string, id: string, data: any, options: any = {}) {
    try {
      const { user, adminUser, tenant, error } = await serverAuth.getUser()
      
      if (error || !user || !adminUser) {
        return { data: null, error: error || 'Authentication required' }
      }

      let updateData = { ...data }

      // Add user context if specified
      if (options.addUserContext) {
        updateData.updatedBy = user.id
        updateData.updatedAt = new Date().toISOString()
      }

      let query = this.client.from(table).update(updateData).eq('id', id)

      // Add tenant filtering for security
      if (options.tenantFiltering !== false && tenant) {
        query = query.eq('tenantId', tenant.id)
      }

      return await query
    } catch (error) {
      return { data: null, error }
    }
  }

  /**
   * Delete with tenant validation
   */
  async delete(table: string, id: string, options: any = {}) {
    try {
      const { user, adminUser, tenant, error } = await serverAuth.getUser()
      
      if (error || !user || !adminUser) {
        return { data: null, error: error || 'Authentication required' }
      }

      let query = this.client.from(table).delete().eq('id', id)

      // Add tenant filtering for security
      if (options.tenantFiltering !== false && tenant) {
        query = query.eq('tenantId', tenant.id)
      }

      return await query
    } catch (error) {
      return { data: null, error }
    }
  }
}

/**
 * Tenant-aware server client instance
 */
export const tenantServerClient = new TenantAwareServerClient()

export default createClient