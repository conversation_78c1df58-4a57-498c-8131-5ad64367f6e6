/**
 * Supabase Client-side Configuration for B2B Multi-Tenant SaaS
 * Enhanced with security features and tenant context management
 */

import { createBrowserClient } from '@supabase/ssr'
import { Database } from '@/types/database'

// Enhanced client configuration for B2B SaaS
export function createClient() {
  return createBrowserClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      auth: {
        // Enhanced security settings for B2B
        autoRefreshToken: true,
        persistSession: true,
        detectSessionInUrl: true,
        flowType: 'pkce', // More secure than implicit flow
        
        // Custom storage for tenant context
        storage: {
          getItem: (key: string) => {
            if (typeof window === 'undefined') return null
            return window.localStorage.getItem(key)
          },
          setItem: (key: string, value: string) => {
            if (typeof window === 'undefined') return
            window.localStorage.setItem(key, value)
          },
          removeItem: (key: string) => {
            if (typeof window === 'undefined') return
            window.localStorage.removeItem(key)
          },
        },
      },
      
      // Enhanced database settings
      db: {
        schema: 'public'
      },
      
      // Real-time configuration for order management
      realtime: {
        params: {
          eventsPerSecond: 10
        }
      },
      
      // Global headers for tenant context
      global: {
        headers: {
          'X-Client-Info': 'bheemdine-web@1.0.0',
        },
      },
    }
  )
}

/**
 * Client-side Supabase instance with enhanced security
 */
export const supabase = createClient()

/**
 * Enhanced authentication helpers for B2B tenant management
 */
export class BHEEMDINEAuthClient {
  private client = createClient()

  /**
   * Sign in with tenant context
   */
  async signInWithTenant(email: string, password: string, tenantSlug?: string) {
    try {
      const { data, error } = await this.client.auth.signInWithPassword({
        email,
        password,
      })

      if (error) throw error

      // Store tenant context if provided
      if (tenantSlug && typeof window !== 'undefined') {
        localStorage.setItem('bheemdine_tenant_slug', tenantSlug)
      }

      return { data, error: null }
    } catch (error) {
      return { data: null, error }
    }
  }

  /**
   * Sign out and clear tenant context
   */
  async signOut() {
    try {
      const { error } = await this.client.auth.signOut()
      
      // Clear tenant context
      if (typeof window !== 'undefined') {
        localStorage.removeItem('bheemdine_tenant_slug')
        localStorage.removeItem('bheemdine_user_profile')
      }

      return { error }
    } catch (error) {
      return { error }
    }
  }

  /**
   * Get current session with tenant validation
   */
  async getSessionWithTenant() {
    try {
      const { data: { session }, error } = await this.client.auth.getSession()
      
      if (error || !session) {
        return { session: null, tenant: null, error }
      }

      // Get tenant context from storage
      const tenantSlug = typeof window !== 'undefined' 
        ? localStorage.getItem('bheemdine_tenant_slug') 
        : null

      return { session, tenant: tenantSlug, error: null }
    } catch (error) {
      return { session: null, tenant: null, error }
    }
  }

  /**
   * Refresh session and validate tenant access
   */
  async refreshSession() {
    try {
      const { data, error } = await this.client.auth.refreshSession()
      
      if (error) throw error

      return { data, error: null }
    } catch (error) {
      return { data: null, error }
    }
  }

  /**
   * Listen to auth state changes with tenant context
   */
  onAuthStateChange(callback: (event: string, session: any, tenant?: string) => void) {
    return this.client.auth.onAuthStateChange((event, session) => {
      const tenant = typeof window !== 'undefined' 
        ? localStorage.getItem('bheemdine_tenant_slug') 
        : undefined
      
      callback(event, session, tenant)
    })
  }

  /**
   * Get user profile with tenant information
   */
  async getUserProfile() {
    try {
      const { data: { user }, error } = await this.client.auth.getUser()
      
      if (error || !user) {
        return { profile: null, error }
      }

      // Fetch admin user profile from database
      const { data: adminUser, error: profileError } = await this.client
        .from('AdminUser')
        .select(`
          *,
          tenant:Tenant(*)
        `)
        .eq('authUserId', user.id)
        .eq('status', 'ACTIVE')
        .single()

      if (profileError) {
        return { profile: null, error: profileError }
      }

      return { 
        profile: {
          ...user,
          adminUser,
          tenant: adminUser?.tenant
        }, 
        error: null 
      }
    } catch (error) {
      return { profile: null, error }
    }
  }

  /**
   * Update user profile
   */
  async updateProfile(updates: {
    email?: string
    password?: string
    data?: Record<string, any>
  }) {
    try {
      const { data, error } = await this.client.auth.updateUser(updates)
      return { data, error }
    } catch (error) {
      return { data: null, error }
    }
  }

  /**
   * Reset password with tenant context
   */
  async resetPassword(email: string, tenantSlug?: string) {
    try {
      const redirectTo = tenantSlug 
        ? `${window.location.origin}/auth/reset-password?tenant=${tenantSlug}`
        : `${window.location.origin}/auth/reset-password`

      const { data, error } = await this.client.auth.resetPasswordForEmail(
        email,
        { redirectTo }
      )

      return { data, error }
    } catch (error) {
      return { data: null, error }
    }
  }

  /**
   * Get accessible tenants for current user
   */
  async getAccessibleTenants() {
    try {
      const { data: { user }, error: userError } = await this.client.auth.getUser()
      
      if (userError || !user) {
        return { tenants: [], error: userError }
      }

      const { data: tenants, error } = await this.client
        .from('AdminUser')
        .select(`
          tenant:Tenant(
            id,
            name,
            slug,
            logoUrl,
            status,
            planType
          )
        `)
        .eq('authUserId', user.id)
        .eq('status', 'ACTIVE')

      return { 
        tenants: tenants?.map(t => t.tenant).filter(Boolean) || [], 
        error 
      }
    } catch (error) {
      return { tenants: [], error }
    }
  }

  /**
   * Switch tenant context
   */
  async switchTenant(tenantSlug: string) {
    try {
      // Validate access to tenant
      const { tenants, error } = await this.getAccessibleTenants()
      
      if (error) throw error

      const hasAccess = tenants.some((t: any) => t.slug === tenantSlug)
      
      if (!hasAccess) {
        throw new Error('Access denied to tenant')
      }

      // Update local storage
      if (typeof window !== 'undefined') {
        localStorage.setItem('bheemdine_tenant_slug', tenantSlug)
      }

      return { success: true, error: null }
    } catch (error) {
      return { success: false, error }
    }
  }
}

/**
 * Enhanced auth client instance
 */
export const authClient = new BHEEMDINEAuthClient()

/**
 * Database query helpers with tenant isolation
 */
export class TenantAwareClient {
  private client = createClient()
  private tenantSlug: string | null = null

  constructor(tenantSlug?: string) {
    this.tenantSlug = tenantSlug || (
      typeof window !== 'undefined' 
        ? localStorage.getItem('bheemdine_tenant_slug')
        : null
    )
  }

  /**
   * Query with automatic tenant filtering
   */
  async query(table: string, options: any = {}) {
    try {
      let query = this.client.from(table)

      // Add tenant filtering if tenant context is available
      if (this.tenantSlug && options.tenantFiltering !== false) {
        // Get tenant ID from slug
        const { data: tenant } = await this.client
          .from('Tenant')
          .select('id')
          .eq('slug', this.tenantSlug)
          .single()

        if (tenant) {
          query = query.eq('tenantId', tenant.id)
        }
      }

      // Apply other filters
      if (options.select) query = query.select(options.select)
      if (options.eq) {
        Object.entries(options.eq).forEach(([key, value]) => {
          query = query.eq(key, value)
        })
      }
      if (options.order) query = query.order(options.order.column, options.order.options)
      if (options.limit) query = query.limit(options.limit)

      return await query
    } catch (error) {
      return { data: null, error }
    }
  }

  /**
   * Insert with automatic tenant assignment
   */
  async insert(table: string, data: any, options: any = {}) {
    try {
      let insertData = { ...data }

      // Add tenant ID if not present and tenant context is available
      if (this.tenantSlug && !insertData.tenantId && options.autoTenant !== false) {
        const { data: tenant } = await this.client
          .from('Tenant')
          .select('id')
          .eq('slug', this.tenantSlug)
          .single()

        if (tenant) {
          insertData.tenantId = tenant.id
        }
      }

      return await this.client.from(table).insert(insertData)
    } catch (error) {
      return { data: null, error }
    }
  }

  /**
   * Update with tenant validation
   */
  async update(table: string, id: string, data: any, options: any = {}) {
    try {
      let query = this.client.from(table).update(data).eq('id', id)

      // Add tenant filtering for security
      if (this.tenantSlug && options.tenantFiltering !== false) {
        const { data: tenant } = await this.client
          .from('Tenant')
          .select('id')
          .eq('slug', this.tenantSlug)
          .single()

        if (tenant) {
          query = query.eq('tenantId', tenant.id)
        }
      }

      return await query
    } catch (error) {
      return { data: null, error }
    }
  }

  /**
   * Delete with tenant validation
   */
  async delete(table: string, id: string, options: any = {}) {
    try {
      let query = this.client.from(table).delete().eq('id', id)

      // Add tenant filtering for security
      if (this.tenantSlug && options.tenantFiltering !== false) {
        const { data: tenant } = await this.client
          .from('Tenant')
          .select('id')
          .eq('slug', this.tenantSlug)
          .single()

        if (tenant) {
          query = query.eq('tenantId', tenant.id)
        }
      }

      return await query
    } catch (error) {
      return { data: null, error }
    }
  }
}

/**
 * Real-time subscriptions with tenant isolation
 */
export class TenantRealtimeClient {
  private client = createClient()
  private tenantSlug: string | null = null

  constructor(tenantSlug?: string) {
    this.tenantSlug = tenantSlug || (
      typeof window !== 'undefined' 
        ? localStorage.getItem('bheemdine_tenant_slug')
        : null
    )
  }

  /**
   * Subscribe to table changes with tenant filtering
   */
  async subscribe(table: string, callback: (payload: any) => void, options: any = {}) {
    try {
      let channel = this.client.channel(`${table}_changes`)

      // Add tenant filtering if available
      if (this.tenantSlug && options.tenantFiltering !== false) {
        const { data: tenant } = await this.client
          .from('Tenant')
          .select('id')
          .eq('slug', this.tenantSlug)
          .single()

        if (tenant) {
          channel = channel.on(
            'postgres_changes',
            {
              event: '*',
              schema: 'public',
              table: table,
              filter: `tenantId=eq.${tenant.id}`
            },
            callback
          )
        }
      } else {
        channel = channel.on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: table
          },
          callback
        )
      }

      return channel.subscribe()
    } catch (error) {
      console.error('Subscription error:', error)
      return null
    }
  }
}

export default supabase