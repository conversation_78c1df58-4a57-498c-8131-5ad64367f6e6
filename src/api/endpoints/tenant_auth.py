"""
Enhanced B2B Multi-Tenant Authentication API
Secure tenant signup and login for hospitality industry SaaS platform
"""

import os
import re
import secrets
import hashlib
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List
from dataclasses import dataclass
from email_validator import validate_email, EmailNotValidError

from fastapi import APIRouter, HTTPException, Depends, Request, Response, BackgroundTasks
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from pydantic import BaseModel, validator, EmailStr, Field
from supabase import create_client, Client
import jwt
from passlib.context import CryptContext

# Database and models
from ..database.prisma_client import get_prisma_client
from ..models.tenant_auth import (
    TenantSignupRequest, TenantLoginRequest, TenantSignupResponse, 
    TenantLoginResponse, TenantValidationRequest, AdminUserProfile,
    TenantOnboardingData, SecurityAuditLog
)

# Security utilities
from ..services.security_service import SecurityService, RateLimiter, InputValidator
from ..services.notification_service import NotificationService
from ..services.audit_service import AuditService

router = APIRouter(prefix="/api/auth", tags=["B2B Authentication"])
security = HTTPBearer()
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# Initialize Supabase client
supabase: Client = create_client(
    os.getenv("SUPABASE_URL"),
    os.getenv("SUPABASE_SERVICE_ROLE_KEY")
)

# Security services
security_service = SecurityService()
rate_limiter = RateLimiter()
input_validator = InputValidator()
notification_service = NotificationService()
audit_service = AuditService()

@dataclass
class TenantAuthConfig:
    """B2B SaaS authentication configuration"""
    PASSWORD_MIN_LENGTH = 12
    PASSWORD_REQUIRE_UPPERCASE = True
    PASSWORD_REQUIRE_LOWERCASE = True
    PASSWORD_REQUIRE_DIGITS = True
    PASSWORD_REQUIRE_SPECIAL = True
    MAX_LOGIN_ATTEMPTS = 5
    LOCKOUT_DURATION_MINUTES = 30
    SESSION_TIMEOUT_HOURS = 8
    REQUIRE_EMAIL_VERIFICATION = True
    TENANT_SLUG_MIN_LENGTH = 3
    TENANT_SLUG_MAX_LENGTH = 50

config = TenantAuthConfig()

# =====================================================
# TENANT SIGNUP ENDPOINT
# =====================================================

@router.post("/signup", response_model=TenantSignupResponse)
async def tenant_signup(
    request: TenantSignupRequest,
    req: Request,
    background_tasks: BackgroundTasks,
    prisma=Depends(get_prisma_client)
) -> TenantSignupResponse:
    """
    B2B Tenant Signup - Complete tenant onboarding with admin user creation
    
    Features:
    - Multi-tenant data isolation
    - Secure password validation
    - Unique tenant slug validation
    - Automated admin user provisioning
    - Email verification workflow
    - Security audit logging
    """
    
    try:
        # Step 1: Rate limiting and basic security
        client_ip = req.client.host
        await rate_limiter.check_rate_limit(f"signup:{client_ip}", max_attempts=3, window_minutes=15)
        
        # Step 2: Comprehensive input validation
        validation_result = await _validate_tenant_signup_data(request, prisma)
        if not validation_result.is_valid:
            raise HTTPException(
                status_code=400,
                detail={
                    "error": "validation_failed",
                    "message": "Signup data validation failed",
                    "errors": validation_result.errors
                }
            )
        
        # Step 3: Password security validation
        password_validation = security_service.validate_password(
            request.admin_password,
            min_length=config.PASSWORD_MIN_LENGTH,
            require_uppercase=config.PASSWORD_REQUIRE_UPPERCASE,
            require_lowercase=config.PASSWORD_REQUIRE_LOWERCASE,
            require_digits=config.PASSWORD_REQUIRE_DIGITS,
            require_special=config.PASSWORD_REQUIRE_SPECIAL
        )
        
        if not password_validation.is_valid:
            raise HTTPException(
                status_code=400,
                detail={
                    "error": "password_requirements_not_met",
                    "message": "Password does not meet security requirements",
                    "requirements": password_validation.failed_requirements
                }
            )
        
        # Step 4: Create Supabase Auth user (admin)
        auth_response = supabase.auth.admin.create_user({
            "email": request.admin_email,
            "password": request.admin_password,
            "email_confirm": not config.REQUIRE_EMAIL_VERIFICATION,
            "user_metadata": {
                "name": request.admin_name,
                "role": "tenant_admin",
                "tenant_slug": request.tenant_slug,
                "signup_ip": client_ip,
                "signup_timestamp": datetime.utcnow().isoformat()
            }
        })
        
        if auth_response.user is None:
            raise HTTPException(
                status_code=500,
                detail={
                    "error": "auth_user_creation_failed",
                    "message": "Failed to create authentication user"
                }
            )
        
        auth_user_id = auth_response.user.id
        
        # Step 5: Create tenant record with business information
        tenant_data = {
            "name": request.business_name,
            "slug": request.tenant_slug,
            "email": request.admin_email,
            "phone": request.business_phone,
            "address": request.business_address,
            "city": request.business_city,
            "state": request.business_state,
            "country": request.business_country or "US",
            "zipCode": request.business_zip,
            "description": request.business_description,
            "website": request.business_website,
            "planType": "TRIAL",  # 30-day trial for new signups
            "status": "PENDING" if config.REQUIRE_EMAIL_VERIFICATION else "ACTIVE",
            "features": {
                "qr_ordering": True,
                "realtime_orders": True,
                "analytics": True,
                "whatsapp_notifications": True,
                "multi_location": False  # Upgrade feature
            },
            "settings": {
                "timezone": request.timezone or "UTC",
                "currency": request.currency or "USD",
                "language": request.language or "en",
                "business_type": request.business_type,
                "setup_completed": False
            }
        }
        
        tenant = await prisma.tenant.create(data=tenant_data)
        
        # Step 6: Create admin user profile linked to tenant
        admin_user_data = {
            "tenantId": tenant.id,
            "authUserId": auth_user_id,
            "email": request.admin_email,
            "firstName": request.admin_name.split()[0] if request.admin_name else "",
            "lastName": " ".join(request.admin_name.split()[1:]) if len(request.admin_name.split()) > 1 else "",
            "phone": request.admin_phone,
            "role": "OWNER",  # Tenant owner has full access
            "status": "PENDING" if config.REQUIRE_EMAIL_VERIFICATION else "ACTIVE",
            "permissions": [],  # Owner gets all permissions by default
            "timezone": request.timezone or "UTC",
            "language": request.language or "en"
        }
        
        admin_user = await prisma.adminuser.create(data=admin_user_data)
        
        # Step 7: Create initial tenant setup data
        await _create_initial_tenant_data(tenant.id, prisma)
        
        # Step 8: Send welcome email and verification if required
        if config.REQUIRE_EMAIL_VERIFICATION:
            background_tasks.add_task(
                notification_service.send_tenant_verification_email,
                request.admin_email,
                request.business_name,
                tenant.slug,
                auth_user_id
            )
        else:
            background_tasks.add_task(
                notification_service.send_tenant_welcome_email,
                request.admin_email,
                request.business_name,
                tenant.slug
            )
        
        # Step 9: Security audit logging
        await audit_service.log_security_event(
            event_type="tenant_signup",
            user_id=auth_user_id,
            tenant_id=tenant.id,
            ip_address=client_ip,
            user_agent=req.headers.get("user-agent", ""),
            metadata={
                "business_name": request.business_name,
                "tenant_slug": request.tenant_slug,
                "business_type": request.business_type,
                "verification_required": config.REQUIRE_EMAIL_VERIFICATION
            }
        )
        
        # Step 10: Generate initial session token (if email verification not required)
        session_token = None
        if not config.REQUIRE_EMAIL_VERIFICATION:
            session_token = await _generate_tenant_session_token(
                auth_user_id, tenant.id, admin_user.id, tenant.slug
            )
        
        return TenantSignupResponse(
            success=True,
            message="Tenant signup successful" + 
                   (" - please verify your email" if config.REQUIRE_EMAIL_VERIFICATION else ""),
            tenant_id=tenant.id,
            tenant_slug=tenant.slug,
            admin_user_id=admin_user.id,
            requires_verification=config.REQUIRE_EMAIL_VERIFICATION,
            session_token=session_token,
            dashboard_url=f"/admin/dashboard?tenant={tenant.slug}",
            trial_expires_at=(datetime.utcnow() + timedelta(days=30)).isoformat()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        # Log unexpected errors
        await audit_service.log_security_event(
            event_type="tenant_signup_error",
            ip_address=client_ip,
            metadata={"error": str(e), "email": request.admin_email}
        )
        raise HTTPException(
            status_code=500,
            detail={
                "error": "signup_failed",
                "message": "An unexpected error occurred during signup"
            }
        )

# =====================================================
# TENANT LOGIN ENDPOINT
# =====================================================

@router.post("/login", response_model=TenantLoginResponse)
async def tenant_login(
    request: TenantLoginRequest,
    req: Request,
    response: Response,
    prisma=Depends(get_prisma_client)
) -> TenantLoginResponse:
    """
    B2B Tenant Admin Login with enhanced security
    
    Features:
    - Multi-tenant context validation
    - Account lockout protection
    - Session management
    - Security audit logging
    - Role-based access control
    """
    
    try:
        client_ip = req.client.host
        
        # Step 1: Rate limiting
        await rate_limiter.check_rate_limit(f"login:{client_ip}", max_attempts=10, window_minutes=15)
        await rate_limiter.check_rate_limit(f"login:email:{request.email}", max_attempts=5, window_minutes=30)
        
        # Step 2: Check for account lockout
        lockout_check = await _check_account_lockout(request.email, prisma)
        if lockout_check.is_locked:
            raise HTTPException(
                status_code=423,
                detail={
                    "error": "account_locked",
                    "message": f"Account locked due to too many failed attempts. Try again in {lockout_check.remaining_minutes} minutes.",
                    "locked_until": lockout_check.locked_until.isoformat()
                }
            )
        
        # Step 3: Validate tenant access (if tenant slug provided)
        if request.tenant_slug:
            tenant_access = await _validate_tenant_access(request.email, request.tenant_slug, prisma)
            if not tenant_access.has_access:
                await _log_login_attempt(request.email, False, "invalid_tenant_access", client_ip, prisma)
                raise HTTPException(
                    status_code=403,
                    detail={
                        "error": "tenant_access_denied",
                        "message": "You do not have access to this tenant"
                    }
                )
        
        # Step 4: Authenticate with Supabase
        auth_response = supabase.auth.sign_in_with_password({
            "email": request.email,
            "password": request.password
        })
        
        if not auth_response.user or not auth_response.session:
            await _log_login_attempt(request.email, False, "invalid_credentials", client_ip, prisma)
            raise HTTPException(
                status_code=401,
                detail={
                    "error": "invalid_credentials",
                    "message": "Invalid email or password"
                }
            )
        
        auth_user = auth_response.user
        session = auth_response.session
        
        # Step 5: Get admin user profile and verify permissions
        admin_user = await prisma.adminuser.find_unique(
            where={"authUserId": auth_user.id},
            include={"tenant": True}
        )
        
        if not admin_user or admin_user.status != "ACTIVE":
            await _log_login_attempt(request.email, False, "account_inactive", client_ip, prisma)
            raise HTTPException(
                status_code=403,
                detail={
                    "error": "account_inactive",
                    "message": "Account is not active"
                }
            )
        
        if not admin_user.tenant.isActive:
            await _log_login_attempt(request.email, False, "tenant_inactive", client_ip, prisma)
            raise HTTPException(
                status_code=403,
                detail={
                    "error": "tenant_inactive", 
                    "message": "Tenant account is not active"
                }
            )
        
        # Step 6: Validate tenant context if specified
        if request.tenant_slug and admin_user.tenant.slug != request.tenant_slug:
            await _log_login_attempt(request.email, False, "tenant_mismatch", client_ip, prisma)
            raise HTTPException(
                status_code=403,
                detail={
                    "error": "tenant_mismatch",
                    "message": "User does not belong to specified tenant"
                }
            )
        
        # Step 7: Update last login and clear failed attempts
        await prisma.adminuser.update(
            where={"id": admin_user.id},
            data={
                "lastLoginAt": datetime.utcnow(),
                "isOnline": True
            }
        )
        
        await _clear_failed_login_attempts(request.email, prisma)
        
        # Step 8: Generate enhanced session token
        session_token = await _generate_tenant_session_token(
            auth_user.id,
            admin_user.tenant.id,
            admin_user.id,
            admin_user.tenant.slug,
            admin_user.role,
            admin_user.permissions
        )
        
        # Step 9: Set secure session cookie if remember_me
        if request.remember_me:
            response.set_cookie(
                key="tenant_session",
                value=session_token,
                max_age=30 * 24 * 60 * 60,  # 30 days
                httponly=True,
                secure=True,
                samesite="strict"
            )
        
        # Step 10: Security audit logging
        await audit_service.log_security_event(
            event_type="tenant_login_success",
            user_id=auth_user.id,
            tenant_id=admin_user.tenant.id,
            ip_address=client_ip,
            user_agent=req.headers.get("user-agent", ""),
            metadata={
                "admin_user_id": admin_user.id,
                "tenant_slug": admin_user.tenant.slug,
                "role": admin_user.role,
                "remember_me": request.remember_me
            }
        )
        
        await _log_login_attempt(request.email, True, "success", client_ip, prisma)
        
        return TenantLoginResponse(
            success=True,
            message="Login successful",
            session_token=session_token,
            user_profile=AdminUserProfile(
                id=admin_user.id,
                auth_user_id=auth_user.id,
                email=admin_user.email,
                first_name=admin_user.firstName,
                last_name=admin_user.lastName,
                phone=admin_user.phone,
                avatar=admin_user.avatar,
                role=admin_user.role,
                permissions=admin_user.permissions,
                tenant_id=admin_user.tenant.id,
                tenant_name=admin_user.tenant.name,
                tenant_slug=admin_user.tenant.slug,
                timezone=admin_user.timezone,
                language=admin_user.language,
                last_login_at=admin_user.lastLoginAt.isoformat() if admin_user.lastLoginAt else None
            ),
            tenant_info={
                "id": admin_user.tenant.id,
                "name": admin_user.tenant.name,
                "slug": admin_user.tenant.slug,
                "plan_type": admin_user.tenant.planType,
                "features": admin_user.tenant.features,
                "settings": admin_user.tenant.settings
            },
            dashboard_url=f"/admin/dashboard?tenant={admin_user.tenant.slug}",
            session_expires_at=(datetime.utcnow() + timedelta(hours=config.SESSION_TIMEOUT_HOURS)).isoformat()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        await audit_service.log_security_event(
            event_type="tenant_login_error",
            ip_address=client_ip,
            metadata={"error": str(e), "email": request.email}
        )
        raise HTTPException(
            status_code=500,
            detail={
                "error": "login_failed",
                "message": "An unexpected error occurred during login"
            }
        )

# =====================================================
# TENANT VALIDATION ENDPOINT
# =====================================================

@router.post("/validate-tenant")
async def validate_tenant_availability(
    request: TenantValidationRequest,
    prisma=Depends(get_prisma_client)
) -> Dict[str, Any]:
    """
    Validate tenant slug and email availability for signup form
    """
    
    try:
        results = {
            "slug_available": True,
            "email_available": True,
            "slug_suggestions": [],
            "validation_errors": []
        }
        
        # Validate tenant slug
        if request.tenant_slug:
            slug_validation = await _validate_tenant_slug(request.tenant_slug, prisma)
            results["slug_available"] = slug_validation.is_available
            results["slug_suggestions"] = slug_validation.suggestions
            if not slug_validation.is_valid:
                results["validation_errors"].extend(slug_validation.errors)
        
        # Validate email availability
        if request.email:
            email_validation = await _validate_email_availability(request.email, prisma)
            results["email_available"] = email_validation.is_available
            if not email_validation.is_valid:
                results["validation_errors"].extend(email_validation.errors)
        
        return results
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail={"error": "validation_failed", "message": str(e)}
        )

# =====================================================
# JWT MIDDLEWARE FOR PROTECTED ROUTES
# =====================================================

async def verify_tenant_admin_token(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    prisma=Depends(get_prisma_client)
) -> AdminUserProfile:
    """
    JWT middleware to verify tenant admin authentication and authorization
    
    Features:
    - JWT token validation
    - Session expiry checking
    - Role-based access control
    - Tenant context validation
    """
    
    try:
        # Decode JWT token
        token = credentials.credentials
        payload = jwt.decode(
            token,
            os.getenv("JWT_SECRET_KEY"),
            algorithms=["HS256"]
        )
        
        # Verify token expiry
        if payload.get("exp", 0) < datetime.utcnow().timestamp():
            raise HTTPException(
                status_code=401,
                detail={"error": "token_expired", "message": "Session has expired"}
            )
        
        # Get user information from payload
        auth_user_id = payload.get("sub")
        tenant_id = payload.get("tenant_id")
        admin_user_id = payload.get("admin_user_id")
        
        if not all([auth_user_id, tenant_id, admin_user_id]):
            raise HTTPException(
                status_code=401,
                detail={"error": "invalid_token", "message": "Token is malformed"}
            )
        
        # Verify admin user is still active
        admin_user = await prisma.adminuser.find_unique(
            where={"id": admin_user_id},
            include={"tenant": True}
        )
        
        if not admin_user or admin_user.status != "ACTIVE":
            raise HTTPException(
                status_code=401,
                detail={"error": "user_inactive", "message": "User account is not active"}
            )
        
        if not admin_user.tenant.isActive:
            raise HTTPException(
                status_code=401,
                detail={"error": "tenant_inactive", "message": "Tenant account is not active"}
            )
        
        # Return user profile for use in protected routes
        return AdminUserProfile(
            id=admin_user.id,
            auth_user_id=admin_user.authUserId,
            email=admin_user.email,
            first_name=admin_user.firstName,
            last_name=admin_user.lastName,
            phone=admin_user.phone,
            avatar=admin_user.avatar,
            role=admin_user.role,
            permissions=admin_user.permissions,
            tenant_id=admin_user.tenant.id,
            tenant_name=admin_user.tenant.name,
            tenant_slug=admin_user.tenant.slug,
            timezone=admin_user.timezone,
            language=admin_user.language,
            last_login_at=admin_user.lastLoginAt.isoformat() if admin_user.lastLoginAt else None
        )
        
    except jwt.InvalidTokenError:
        raise HTTPException(
            status_code=401,
            detail={"error": "invalid_token", "message": "Invalid authentication token"}
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=401,
            detail={"error": "auth_failed", "message": "Authentication failed"}
        )

# =====================================================
# HELPER FUNCTIONS
# =====================================================

async def _validate_tenant_signup_data(request: TenantSignupRequest, prisma) -> Any:
    """Comprehensive validation of tenant signup data"""
    errors = []
    
    # Business name validation
    if not request.business_name or len(request.business_name.strip()) < 2:
        errors.append("Business name must be at least 2 characters long")
    
    # Email validation
    try:
        validate_email(request.admin_email)
    except EmailNotValidError:
        errors.append("Invalid email address format")
    
    # Check email uniqueness across all tenants
    existing_email = await prisma.adminuser.find_first(
        where={"email": request.admin_email}
    )
    if existing_email:
        errors.append("Email address is already registered")
    
    # Tenant slug validation
    slug_validation = await _validate_tenant_slug(request.tenant_slug, prisma)
    if not slug_validation.is_valid:
        errors.extend(slug_validation.errors)
    
    return type('ValidationResult', (), {
        'is_valid': len(errors) == 0,
        'errors': errors
    })()

async def _validate_tenant_slug(slug: str, prisma) -> Any:
    """Validate tenant slug format and availability"""
    errors = []
    suggestions = []
    
    # Format validation
    if not slug or len(slug) < config.TENANT_SLUG_MIN_LENGTH:
        errors.append(f"Tenant slug must be at least {config.TENANT_SLUG_MIN_LENGTH} characters long")
    
    if len(slug) > config.TENANT_SLUG_MAX_LENGTH:
        errors.append(f"Tenant slug must be no more than {config.TENANT_SLUG_MAX_LENGTH} characters long")
    
    if not re.match(r'^[a-z0-9-]+$', slug):
        errors.append("Tenant slug can only contain lowercase letters, numbers, and hyphens")
    
    if slug.startswith('-') or slug.endswith('-'):
        errors.append("Tenant slug cannot start or end with a hyphen")
    
    # Check availability
    existing_tenant = await prisma.tenant.find_first(
        where={"slug": slug}
    )
    
    is_available = existing_tenant is None
    
    # Generate suggestions if not available
    if not is_available:
        base_slug = slug.rstrip('0123456789')
        for i in range(1, 6):
            suggestion = f"{base_slug}{i}"
            existing = await prisma.tenant.find_first(where={"slug": suggestion})
            if not existing:
                suggestions.append(suggestion)
    
    return type('SlugValidation', (), {
        'is_valid': len(errors) == 0,
        'is_available': is_available,
        'errors': errors,
        'suggestions': suggestions
    })()

async def _generate_tenant_session_token(
    auth_user_id: str,
    tenant_id: str,
    admin_user_id: str,
    tenant_slug: str,
    role: str = "OWNER",
    permissions: List[str] = None
) -> str:
    """Generate JWT session token with tenant context"""
    
    payload = {
        "sub": auth_user_id,
        "tenant_id": tenant_id,
        "admin_user_id": admin_user_id,
        "tenant_slug": tenant_slug,
        "role": role,
        "permissions": permissions or [],
        "iat": datetime.utcnow().timestamp(),
        "exp": (datetime.utcnow() + timedelta(hours=config.SESSION_TIMEOUT_HOURS)).timestamp(),
        "iss": "bheemdine-auth",
        "aud": "bheemdine-admin"
    }
    
    return jwt.encode(
        payload,
        os.getenv("JWT_SECRET_KEY"),
        algorithm="HS256"
    )

async def _create_initial_tenant_data(tenant_id: str, prisma) -> None:
    """Create initial setup data for new tenant"""
    
    # Create default menu categories
    default_categories = [
        {"name": "Appetizers", "sortOrder": 1},
        {"name": "Main Course", "sortOrder": 2},
        {"name": "Desserts", "sortOrder": 3},
        {"name": "Beverages", "sortOrder": 4}
    ]
    
    # Create sample rooms/tables
    sample_rooms = [
        {"roomNumber": "T001", "floor": "Ground Floor", "capacity": 4},
        {"roomNumber": "T002", "floor": "Ground Floor", "capacity": 2},
        {"roomNumber": "R101", "floor": "First Floor", "capacity": 2}
    ]
    
    # Create initial allergens
    common_allergens = [
        {"name": "Gluten", "icon": "wheat"},
        {"name": "Dairy", "icon": "milk"},
        {"name": "Nuts", "icon": "nut"},
        {"name": "Eggs", "icon": "egg"},
        {"name": "Soy", "icon": "soy"}
    ]
    
    try:
        # Insert sample data
        for room in sample_rooms:
            await prisma.room.create(data={
                **room,
                "tenantId": tenant_id,
                "qrCode": f"qr_{secrets.token_urlsafe(16)}"
            })
        
        for allergen in common_allergens:
            await prisma.allergen.create(data={
                **allergen,
                "tenantId": tenant_id
            })
            
    except Exception as e:
        # Log error but don't fail signup
        print(f"Warning: Failed to create initial tenant data: {e}")

# Additional helper functions for login, validation, etc...
# (Implementation continues with remaining helper functions)