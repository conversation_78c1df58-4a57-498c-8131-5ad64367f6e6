{"name": "b<PERSON><PERSON><PERSON>", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "sync-db": "node scripts/sync-database-to-supabase.js", "generate-sql": "node scripts/generate-supabase-sql.js", "db-reset": "npx prisma db push --force-reset", "db-studio": "npx prisma studio"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@prisma/client": "^6.11.1", "@supabase/supabase-js": "^2.50.5", "@tanstack/react-query": "^5.83.0", "autoprefixer": "10.4.16", "dotenv": "^17.2.0", "html2canvas": "1.4.1", "jspdf": "2.5.1", "lucide-react": "0.294.0", "next": "14.0.4", "postcss": "8.4.32", "qrcode": "1.5.3", "react": "18.2.0", "react-dom": "18.2.0", "react-hook-form": "^7.60.0", "tailwindcss": "3.4.0", "typescript": "5.3.3", "zod": "^4.0.5", "zustand": "4.4.7"}, "devDependencies": {"@playwright/test": "^1.54.1", "@types/node": "20.10.5", "@types/qrcode": "1.5.5", "@types/react": "18.2.45", "@types/react-dom": "18.2.18", "@typescript-eslint/eslint-plugin": "^8.37.0", "@typescript-eslint/parser": "^8.37.0", "eslint-plugin-import": "^2.32.0", "eslint-plugin-jsx-a11y": "^6.10.2", "playwright": "^1.54.1", "prettier": "^3.6.2"}}