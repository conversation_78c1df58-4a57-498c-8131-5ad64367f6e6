# Development Environment Variables
NODE_ENV=development
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Database (using SQLite for development)
DATABASE_URL="file:./dev.db"

# Supabase Configuration - REPLACE WITH YOUR REAL CREDENTIALS
# Get these from: https://supabase.com/dashboard → Your Project → Settings → API
NEXT_PUBLIC_SUPABASE_URL=https://YOUR_PROJECT_REF.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=YOUR_ANON_PUBLIC_KEY_HERE
SUPABASE_SERVICE_ROLE_KEY=YOUR_SERVICE_ROLE_KEY_HERE

# Authentication
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=development-secret-key-for-testing-only
JWT_SECRET=development-jwt-secret-for-testing-only

# QR Code Configuration
NEXT_PUBLIC_QR_BASE_URL=http://localhost:3000/menu
QR_CODE_SIZE=256
QR_CODE_ERROR_CORRECTION=M
EOF < /dev/null