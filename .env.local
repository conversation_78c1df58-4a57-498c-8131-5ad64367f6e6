# Development Environment Variables
NODE_ENV=development
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Database (using SQLite for development)
DATABASE_URL="file:./dev.db"

# Supabase Configuration - Production Ready
NEXT_PUBLIC_SUPABASE_URL=https://cgzcndxnfldupgdddnra.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNnemNuZHhuZmxkdXBnZGRkbnJhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI1MjU3OTUsImV4cCI6MjA2ODEwMTc5NX0.XZR5Qlhp31SV2rn30rdhDNKd3x402pLWuma9E-Zy2Oo
# SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here
# TODO: Replace with your actual Supabase service role key from the dashboard

# Authentication - Enhanced B2B Security
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=bheemdine-secure-nextauth-secret-for-b2b-saas-2024
JWT_SECRET=bheemdine-super-secure-jwt-secret-key-for-b2b-saas-platform-2024

# B2B Security Settings
PASSWORD_MIN_LENGTH=12
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION_MINUTES=30
REQUIRE_EMAIL_VERIFICATION=true
SESSION_TIMEOUT_HOURS=8

# Multi-Tenant Configuration
ENABLE_MULTI_TENANT=true
DEFAULT_TRIAL_DAYS=30
TENANT_SLUG_MIN_LENGTH=3
TENANT_SLUG_MAX_LENGTH=50

# QR Code Configuration
NEXT_PUBLIC_QR_BASE_URL=http://localhost:3000/menu
QR_CODE_SIZE=256
QR_CODE_ERROR_CORRECTION=M
EOF < /dev/null