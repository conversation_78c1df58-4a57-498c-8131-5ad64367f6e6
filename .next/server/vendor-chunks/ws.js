"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/ws";
exports.ids = ["vendor-chunks/ws"];
exports.modules = {

/***/ "(ssr)/./node_modules/ws/lib/buffer-util.js":
/*!********************************************!*\
  !*** ./node_modules/ws/lib/buffer-util.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst { EMPTY_BUFFER } = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/ws/lib/constants.js\");\nconst FastBuffer = Buffer[Symbol.species];\n/**\n * Merges an array of buffers into a new buffer.\n *\n * @param {Buffer[]} list The array of buffers to concat\n * @param {Number} totalLength The total length of buffers in the list\n * @return {Buffer} The resulting buffer\n * @public\n */ function concat(list, totalLength) {\n    if (list.length === 0) return EMPTY_BUFFER;\n    if (list.length === 1) return list[0];\n    const target = Buffer.allocUnsafe(totalLength);\n    let offset = 0;\n    for(let i = 0; i < list.length; i++){\n        const buf = list[i];\n        target.set(buf, offset);\n        offset += buf.length;\n    }\n    if (offset < totalLength) {\n        return new FastBuffer(target.buffer, target.byteOffset, offset);\n    }\n    return target;\n}\n/**\n * Masks a buffer using the given mask.\n *\n * @param {Buffer} source The buffer to mask\n * @param {Buffer} mask The mask to use\n * @param {Buffer} output The buffer where to store the result\n * @param {Number} offset The offset at which to start writing\n * @param {Number} length The number of bytes to mask.\n * @public\n */ function _mask(source, mask, output, offset, length) {\n    for(let i = 0; i < length; i++){\n        output[offset + i] = source[i] ^ mask[i & 3];\n    }\n}\n/**\n * Unmasks a buffer using the given mask.\n *\n * @param {Buffer} buffer The buffer to unmask\n * @param {Buffer} mask The mask to use\n * @public\n */ function _unmask(buffer, mask) {\n    for(let i = 0; i < buffer.length; i++){\n        buffer[i] ^= mask[i & 3];\n    }\n}\n/**\n * Converts a buffer to an `ArrayBuffer`.\n *\n * @param {Buffer} buf The buffer to convert\n * @return {ArrayBuffer} Converted buffer\n * @public\n */ function toArrayBuffer(buf) {\n    if (buf.length === buf.buffer.byteLength) {\n        return buf.buffer;\n    }\n    return buf.buffer.slice(buf.byteOffset, buf.byteOffset + buf.length);\n}\n/**\n * Converts `data` to a `Buffer`.\n *\n * @param {*} data The data to convert\n * @return {Buffer} The buffer\n * @throws {TypeError}\n * @public\n */ function toBuffer(data) {\n    toBuffer.readOnly = true;\n    if (Buffer.isBuffer(data)) return data;\n    let buf;\n    if (data instanceof ArrayBuffer) {\n        buf = new FastBuffer(data);\n    } else if (ArrayBuffer.isView(data)) {\n        buf = new FastBuffer(data.buffer, data.byteOffset, data.byteLength);\n    } else {\n        buf = Buffer.from(data);\n        toBuffer.readOnly = false;\n    }\n    return buf;\n}\nmodule.exports = {\n    concat,\n    mask: _mask,\n    toArrayBuffer,\n    toBuffer,\n    unmask: _unmask\n};\n/* istanbul ignore else  */ if (!process.env.WS_NO_BUFFER_UTIL) {\n    try {\n        const bufferUtil = __webpack_require__(/*! bufferutil */ \"?32c4\");\n        module.exports.mask = function(source, mask, output, offset, length) {\n            if (length < 48) _mask(source, mask, output, offset, length);\n            else bufferUtil.mask(source, mask, output, offset, length);\n        };\n        module.exports.unmask = function(buffer, mask) {\n            if (buffer.length < 32) _unmask(buffer, mask);\n            else bufferUtil.unmask(buffer, mask);\n        };\n    } catch (e) {\n    // Continue regardless of the error.\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvd3MvbGliL2J1ZmZlci11dGlsLmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBRUEsTUFBTSxFQUFFQSxZQUFZLEVBQUUsR0FBR0MsbUJBQU9BLENBQUM7QUFFakMsTUFBTUMsYUFBYUMsTUFBTSxDQUFDQyxPQUFPQyxPQUFPLENBQUM7QUFFekM7Ozs7Ozs7Q0FPQyxHQUNELFNBQVNDLE9BQU9DLElBQUksRUFBRUMsV0FBVztJQUMvQixJQUFJRCxLQUFLRSxNQUFNLEtBQUssR0FBRyxPQUFPVDtJQUM5QixJQUFJTyxLQUFLRSxNQUFNLEtBQUssR0FBRyxPQUFPRixJQUFJLENBQUMsRUFBRTtJQUVyQyxNQUFNRyxTQUFTUCxPQUFPUSxXQUFXLENBQUNIO0lBQ2xDLElBQUlJLFNBQVM7SUFFYixJQUFLLElBQUlDLElBQUksR0FBR0EsSUFBSU4sS0FBS0UsTUFBTSxFQUFFSSxJQUFLO1FBQ3BDLE1BQU1DLE1BQU1QLElBQUksQ0FBQ00sRUFBRTtRQUNuQkgsT0FBT0ssR0FBRyxDQUFDRCxLQUFLRjtRQUNoQkEsVUFBVUUsSUFBSUwsTUFBTTtJQUN0QjtJQUVBLElBQUlHLFNBQVNKLGFBQWE7UUFDeEIsT0FBTyxJQUFJTixXQUFXUSxPQUFPTSxNQUFNLEVBQUVOLE9BQU9PLFVBQVUsRUFBRUw7SUFDMUQ7SUFFQSxPQUFPRjtBQUNUO0FBRUE7Ozs7Ozs7OztDQVNDLEdBQ0QsU0FBU1EsTUFBTUMsTUFBTSxFQUFFQyxJQUFJLEVBQUVDLE1BQU0sRUFBRVQsTUFBTSxFQUFFSCxNQUFNO0lBQ2pELElBQUssSUFBSUksSUFBSSxHQUFHQSxJQUFJSixRQUFRSSxJQUFLO1FBQy9CUSxNQUFNLENBQUNULFNBQVNDLEVBQUUsR0FBR00sTUFBTSxDQUFDTixFQUFFLEdBQUdPLElBQUksQ0FBQ1AsSUFBSSxFQUFFO0lBQzlDO0FBQ0Y7QUFFQTs7Ozs7O0NBTUMsR0FDRCxTQUFTUyxRQUFRTixNQUFNLEVBQUVJLElBQUk7SUFDM0IsSUFBSyxJQUFJUCxJQUFJLEdBQUdBLElBQUlHLE9BQU9QLE1BQU0sRUFBRUksSUFBSztRQUN0Q0csTUFBTSxDQUFDSCxFQUFFLElBQUlPLElBQUksQ0FBQ1AsSUFBSSxFQUFFO0lBQzFCO0FBQ0Y7QUFFQTs7Ozs7O0NBTUMsR0FDRCxTQUFTVSxjQUFjVCxHQUFHO0lBQ3hCLElBQUlBLElBQUlMLE1BQU0sS0FBS0ssSUFBSUUsTUFBTSxDQUFDUSxVQUFVLEVBQUU7UUFDeEMsT0FBT1YsSUFBSUUsTUFBTTtJQUNuQjtJQUVBLE9BQU9GLElBQUlFLE1BQU0sQ0FBQ1MsS0FBSyxDQUFDWCxJQUFJRyxVQUFVLEVBQUVILElBQUlHLFVBQVUsR0FBR0gsSUFBSUwsTUFBTTtBQUNyRTtBQUVBOzs7Ozs7O0NBT0MsR0FDRCxTQUFTaUIsU0FBU0MsSUFBSTtJQUNwQkQsU0FBU0UsUUFBUSxHQUFHO0lBRXBCLElBQUl6QixPQUFPMEIsUUFBUSxDQUFDRixPQUFPLE9BQU9BO0lBRWxDLElBQUliO0lBRUosSUFBSWEsZ0JBQWdCRyxhQUFhO1FBQy9CaEIsTUFBTSxJQUFJWixXQUFXeUI7SUFDdkIsT0FBTyxJQUFJRyxZQUFZQyxNQUFNLENBQUNKLE9BQU87UUFDbkNiLE1BQU0sSUFBSVosV0FBV3lCLEtBQUtYLE1BQU0sRUFBRVcsS0FBS1YsVUFBVSxFQUFFVSxLQUFLSCxVQUFVO0lBQ3BFLE9BQU87UUFDTFYsTUFBTVgsT0FBTzZCLElBQUksQ0FBQ0w7UUFDbEJELFNBQVNFLFFBQVEsR0FBRztJQUN0QjtJQUVBLE9BQU9kO0FBQ1Q7QUFFQW1CLE9BQU9DLE9BQU8sR0FBRztJQUNmNUI7SUFDQWMsTUFBTUY7SUFDTks7SUFDQUc7SUFDQVMsUUFBUWI7QUFDVjtBQUVBLHlCQUF5QixHQUN6QixJQUFJLENBQUNjLFFBQVFDLEdBQUcsQ0FBQ0MsaUJBQWlCLEVBQUU7SUFDbEMsSUFBSTtRQUNGLE1BQU1DLGFBQWF0QyxtQkFBT0EsQ0FBQztRQUUzQmdDLG1CQUFtQixHQUFHLFNBQVVkLE1BQU0sRUFBRUMsSUFBSSxFQUFFQyxNQUFNLEVBQUVULE1BQU0sRUFBRUgsTUFBTTtZQUNsRSxJQUFJQSxTQUFTLElBQUlTLE1BQU1DLFFBQVFDLE1BQU1DLFFBQVFULFFBQVFIO2lCQUNoRDhCLFdBQVduQixJQUFJLENBQUNELFFBQVFDLE1BQU1DLFFBQVFULFFBQVFIO1FBQ3JEO1FBRUF3QixxQkFBcUIsR0FBRyxTQUFVakIsTUFBTSxFQUFFSSxJQUFJO1lBQzVDLElBQUlKLE9BQU9QLE1BQU0sR0FBRyxJQUFJYSxRQUFRTixRQUFRSTtpQkFDbkNtQixXQUFXSixNQUFNLENBQUNuQixRQUFRSTtRQUNqQztJQUNGLEVBQUUsT0FBT29CLEdBQUc7SUFDVixvQ0FBb0M7SUFDdEM7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL2JoZWVtZGluZS8uL25vZGVfbW9kdWxlcy93cy9saWIvYnVmZmVyLXV0aWwuanM/NmUwZSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbmNvbnN0IHsgRU1QVFlfQlVGRkVSIH0gPSByZXF1aXJlKCcuL2NvbnN0YW50cycpO1xuXG5jb25zdCBGYXN0QnVmZmVyID0gQnVmZmVyW1N5bWJvbC5zcGVjaWVzXTtcblxuLyoqXG4gKiBNZXJnZXMgYW4gYXJyYXkgb2YgYnVmZmVycyBpbnRvIGEgbmV3IGJ1ZmZlci5cbiAqXG4gKiBAcGFyYW0ge0J1ZmZlcltdfSBsaXN0IFRoZSBhcnJheSBvZiBidWZmZXJzIHRvIGNvbmNhdFxuICogQHBhcmFtIHtOdW1iZXJ9IHRvdGFsTGVuZ3RoIFRoZSB0b3RhbCBsZW5ndGggb2YgYnVmZmVycyBpbiB0aGUgbGlzdFxuICogQHJldHVybiB7QnVmZmVyfSBUaGUgcmVzdWx0aW5nIGJ1ZmZlclxuICogQHB1YmxpY1xuICovXG5mdW5jdGlvbiBjb25jYXQobGlzdCwgdG90YWxMZW5ndGgpIHtcbiAgaWYgKGxpc3QubGVuZ3RoID09PSAwKSByZXR1cm4gRU1QVFlfQlVGRkVSO1xuICBpZiAobGlzdC5sZW5ndGggPT09IDEpIHJldHVybiBsaXN0WzBdO1xuXG4gIGNvbnN0IHRhcmdldCA9IEJ1ZmZlci5hbGxvY1Vuc2FmZSh0b3RhbExlbmd0aCk7XG4gIGxldCBvZmZzZXQgPSAwO1xuXG4gIGZvciAobGV0IGkgPSAwOyBpIDwgbGlzdC5sZW5ndGg7IGkrKykge1xuICAgIGNvbnN0IGJ1ZiA9IGxpc3RbaV07XG4gICAgdGFyZ2V0LnNldChidWYsIG9mZnNldCk7XG4gICAgb2Zmc2V0ICs9IGJ1Zi5sZW5ndGg7XG4gIH1cblxuICBpZiAob2Zmc2V0IDwgdG90YWxMZW5ndGgpIHtcbiAgICByZXR1cm4gbmV3IEZhc3RCdWZmZXIodGFyZ2V0LmJ1ZmZlciwgdGFyZ2V0LmJ5dGVPZmZzZXQsIG9mZnNldCk7XG4gIH1cblxuICByZXR1cm4gdGFyZ2V0O1xufVxuXG4vKipcbiAqIE1hc2tzIGEgYnVmZmVyIHVzaW5nIHRoZSBnaXZlbiBtYXNrLlxuICpcbiAqIEBwYXJhbSB7QnVmZmVyfSBzb3VyY2UgVGhlIGJ1ZmZlciB0byBtYXNrXG4gKiBAcGFyYW0ge0J1ZmZlcn0gbWFzayBUaGUgbWFzayB0byB1c2VcbiAqIEBwYXJhbSB7QnVmZmVyfSBvdXRwdXQgVGhlIGJ1ZmZlciB3aGVyZSB0byBzdG9yZSB0aGUgcmVzdWx0XG4gKiBAcGFyYW0ge051bWJlcn0gb2Zmc2V0IFRoZSBvZmZzZXQgYXQgd2hpY2ggdG8gc3RhcnQgd3JpdGluZ1xuICogQHBhcmFtIHtOdW1iZXJ9IGxlbmd0aCBUaGUgbnVtYmVyIG9mIGJ5dGVzIHRvIG1hc2suXG4gKiBAcHVibGljXG4gKi9cbmZ1bmN0aW9uIF9tYXNrKHNvdXJjZSwgbWFzaywgb3V0cHV0LCBvZmZzZXQsIGxlbmd0aCkge1xuICBmb3IgKGxldCBpID0gMDsgaSA8IGxlbmd0aDsgaSsrKSB7XG4gICAgb3V0cHV0W29mZnNldCArIGldID0gc291cmNlW2ldIF4gbWFza1tpICYgM107XG4gIH1cbn1cblxuLyoqXG4gKiBVbm1hc2tzIGEgYnVmZmVyIHVzaW5nIHRoZSBnaXZlbiBtYXNrLlxuICpcbiAqIEBwYXJhbSB7QnVmZmVyfSBidWZmZXIgVGhlIGJ1ZmZlciB0byB1bm1hc2tcbiAqIEBwYXJhbSB7QnVmZmVyfSBtYXNrIFRoZSBtYXNrIHRvIHVzZVxuICogQHB1YmxpY1xuICovXG5mdW5jdGlvbiBfdW5tYXNrKGJ1ZmZlciwgbWFzaykge1xuICBmb3IgKGxldCBpID0gMDsgaSA8IGJ1ZmZlci5sZW5ndGg7IGkrKykge1xuICAgIGJ1ZmZlcltpXSBePSBtYXNrW2kgJiAzXTtcbiAgfVxufVxuXG4vKipcbiAqIENvbnZlcnRzIGEgYnVmZmVyIHRvIGFuIGBBcnJheUJ1ZmZlcmAuXG4gKlxuICogQHBhcmFtIHtCdWZmZXJ9IGJ1ZiBUaGUgYnVmZmVyIHRvIGNvbnZlcnRcbiAqIEByZXR1cm4ge0FycmF5QnVmZmVyfSBDb252ZXJ0ZWQgYnVmZmVyXG4gKiBAcHVibGljXG4gKi9cbmZ1bmN0aW9uIHRvQXJyYXlCdWZmZXIoYnVmKSB7XG4gIGlmIChidWYubGVuZ3RoID09PSBidWYuYnVmZmVyLmJ5dGVMZW5ndGgpIHtcbiAgICByZXR1cm4gYnVmLmJ1ZmZlcjtcbiAgfVxuXG4gIHJldHVybiBidWYuYnVmZmVyLnNsaWNlKGJ1Zi5ieXRlT2Zmc2V0LCBidWYuYnl0ZU9mZnNldCArIGJ1Zi5sZW5ndGgpO1xufVxuXG4vKipcbiAqIENvbnZlcnRzIGBkYXRhYCB0byBhIGBCdWZmZXJgLlxuICpcbiAqIEBwYXJhbSB7Kn0gZGF0YSBUaGUgZGF0YSB0byBjb252ZXJ0XG4gKiBAcmV0dXJuIHtCdWZmZXJ9IFRoZSBidWZmZXJcbiAqIEB0aHJvd3Mge1R5cGVFcnJvcn1cbiAqIEBwdWJsaWNcbiAqL1xuZnVuY3Rpb24gdG9CdWZmZXIoZGF0YSkge1xuICB0b0J1ZmZlci5yZWFkT25seSA9IHRydWU7XG5cbiAgaWYgKEJ1ZmZlci5pc0J1ZmZlcihkYXRhKSkgcmV0dXJuIGRhdGE7XG5cbiAgbGV0IGJ1ZjtcblxuICBpZiAoZGF0YSBpbnN0YW5jZW9mIEFycmF5QnVmZmVyKSB7XG4gICAgYnVmID0gbmV3IEZhc3RCdWZmZXIoZGF0YSk7XG4gIH0gZWxzZSBpZiAoQXJyYXlCdWZmZXIuaXNWaWV3KGRhdGEpKSB7XG4gICAgYnVmID0gbmV3IEZhc3RCdWZmZXIoZGF0YS5idWZmZXIsIGRhdGEuYnl0ZU9mZnNldCwgZGF0YS5ieXRlTGVuZ3RoKTtcbiAgfSBlbHNlIHtcbiAgICBidWYgPSBCdWZmZXIuZnJvbShkYXRhKTtcbiAgICB0b0J1ZmZlci5yZWFkT25seSA9IGZhbHNlO1xuICB9XG5cbiAgcmV0dXJuIGJ1Zjtcbn1cblxubW9kdWxlLmV4cG9ydHMgPSB7XG4gIGNvbmNhdCxcbiAgbWFzazogX21hc2ssXG4gIHRvQXJyYXlCdWZmZXIsXG4gIHRvQnVmZmVyLFxuICB1bm1hc2s6IF91bm1hc2tcbn07XG5cbi8qIGlzdGFuYnVsIGlnbm9yZSBlbHNlICAqL1xuaWYgKCFwcm9jZXNzLmVudi5XU19OT19CVUZGRVJfVVRJTCkge1xuICB0cnkge1xuICAgIGNvbnN0IGJ1ZmZlclV0aWwgPSByZXF1aXJlKCdidWZmZXJ1dGlsJyk7XG5cbiAgICBtb2R1bGUuZXhwb3J0cy5tYXNrID0gZnVuY3Rpb24gKHNvdXJjZSwgbWFzaywgb3V0cHV0LCBvZmZzZXQsIGxlbmd0aCkge1xuICAgICAgaWYgKGxlbmd0aCA8IDQ4KSBfbWFzayhzb3VyY2UsIG1hc2ssIG91dHB1dCwgb2Zmc2V0LCBsZW5ndGgpO1xuICAgICAgZWxzZSBidWZmZXJVdGlsLm1hc2soc291cmNlLCBtYXNrLCBvdXRwdXQsIG9mZnNldCwgbGVuZ3RoKTtcbiAgICB9O1xuXG4gICAgbW9kdWxlLmV4cG9ydHMudW5tYXNrID0gZnVuY3Rpb24gKGJ1ZmZlciwgbWFzaykge1xuICAgICAgaWYgKGJ1ZmZlci5sZW5ndGggPCAzMikgX3VubWFzayhidWZmZXIsIG1hc2spO1xuICAgICAgZWxzZSBidWZmZXJVdGlsLnVubWFzayhidWZmZXIsIG1hc2spO1xuICAgIH07XG4gIH0gY2F0Y2ggKGUpIHtcbiAgICAvLyBDb250aW51ZSByZWdhcmRsZXNzIG9mIHRoZSBlcnJvci5cbiAgfVxufVxuIl0sIm5hbWVzIjpbIkVNUFRZX0JVRkZFUiIsInJlcXVpcmUiLCJGYXN0QnVmZmVyIiwiQnVmZmVyIiwiU3ltYm9sIiwic3BlY2llcyIsImNvbmNhdCIsImxpc3QiLCJ0b3RhbExlbmd0aCIsImxlbmd0aCIsInRhcmdldCIsImFsbG9jVW5zYWZlIiwib2Zmc2V0IiwiaSIsImJ1ZiIsInNldCIsImJ1ZmZlciIsImJ5dGVPZmZzZXQiLCJfbWFzayIsInNvdXJjZSIsIm1hc2siLCJvdXRwdXQiLCJfdW5tYXNrIiwidG9BcnJheUJ1ZmZlciIsImJ5dGVMZW5ndGgiLCJzbGljZSIsInRvQnVmZmVyIiwiZGF0YSIsInJlYWRPbmx5IiwiaXNCdWZmZXIiLCJBcnJheUJ1ZmZlciIsImlzVmlldyIsImZyb20iLCJtb2R1bGUiLCJleHBvcnRzIiwidW5tYXNrIiwicHJvY2VzcyIsImVudiIsIldTX05PX0JVRkZFUl9VVElMIiwiYnVmZmVyVXRpbCIsImUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ws/lib/buffer-util.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ws/lib/constants.js":
/*!******************************************!*\
  !*** ./node_modules/ws/lib/constants.js ***!
  \******************************************/
/***/ ((module) => {

eval("\nconst BINARY_TYPES = [\n    \"nodebuffer\",\n    \"arraybuffer\",\n    \"fragments\"\n];\nconst hasBlob = typeof Blob !== \"undefined\";\nif (hasBlob) BINARY_TYPES.push(\"blob\");\nmodule.exports = {\n    BINARY_TYPES,\n    EMPTY_BUFFER: Buffer.alloc(0),\n    GUID: \"258EAFA5-E914-47DA-95CA-C5AB0DC85B11\",\n    hasBlob,\n    kForOnEventAttribute: Symbol(\"kIsForOnEventAttribute\"),\n    kListener: Symbol(\"kListener\"),\n    kStatusCode: Symbol(\"status-code\"),\n    kWebSocket: Symbol(\"websocket\"),\n    NOOP: ()=>{}\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvd3MvbGliL2NvbnN0YW50cy5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUVBLE1BQU1BLGVBQWU7SUFBQztJQUFjO0lBQWU7Q0FBWTtBQUMvRCxNQUFNQyxVQUFVLE9BQU9DLFNBQVM7QUFFaEMsSUFBSUQsU0FBU0QsYUFBYUcsSUFBSSxDQUFDO0FBRS9CQyxPQUFPQyxPQUFPLEdBQUc7SUFDZkw7SUFDQU0sY0FBY0MsT0FBT0MsS0FBSyxDQUFDO0lBQzNCQyxNQUFNO0lBQ05SO0lBQ0FTLHNCQUFzQkMsT0FBTztJQUM3QkMsV0FBV0QsT0FBTztJQUNsQkUsYUFBYUYsT0FBTztJQUNwQkcsWUFBWUgsT0FBTztJQUNuQkksTUFBTSxLQUFPO0FBQ2YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9iaGVlbWRpbmUvLi9ub2RlX21vZHVsZXMvd3MvbGliL2NvbnN0YW50cy5qcz9jMmEzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuY29uc3QgQklOQVJZX1RZUEVTID0gWydub2RlYnVmZmVyJywgJ2FycmF5YnVmZmVyJywgJ2ZyYWdtZW50cyddO1xuY29uc3QgaGFzQmxvYiA9IHR5cGVvZiBCbG9iICE9PSAndW5kZWZpbmVkJztcblxuaWYgKGhhc0Jsb2IpIEJJTkFSWV9UWVBFUy5wdXNoKCdibG9iJyk7XG5cbm1vZHVsZS5leHBvcnRzID0ge1xuICBCSU5BUllfVFlQRVMsXG4gIEVNUFRZX0JVRkZFUjogQnVmZmVyLmFsbG9jKDApLFxuICBHVUlEOiAnMjU4RUFGQTUtRTkxNC00N0RBLTk1Q0EtQzVBQjBEQzg1QjExJyxcbiAgaGFzQmxvYixcbiAga0Zvck9uRXZlbnRBdHRyaWJ1dGU6IFN5bWJvbCgna0lzRm9yT25FdmVudEF0dHJpYnV0ZScpLFxuICBrTGlzdGVuZXI6IFN5bWJvbCgna0xpc3RlbmVyJyksXG4gIGtTdGF0dXNDb2RlOiBTeW1ib2woJ3N0YXR1cy1jb2RlJyksXG4gIGtXZWJTb2NrZXQ6IFN5bWJvbCgnd2Vic29ja2V0JyksXG4gIE5PT1A6ICgpID0+IHt9XG59O1xuIl0sIm5hbWVzIjpbIkJJTkFSWV9UWVBFUyIsImhhc0Jsb2IiLCJCbG9iIiwicHVzaCIsIm1vZHVsZSIsImV4cG9ydHMiLCJFTVBUWV9CVUZGRVIiLCJCdWZmZXIiLCJhbGxvYyIsIkdVSUQiLCJrRm9yT25FdmVudEF0dHJpYnV0ZSIsIlN5bWJvbCIsImtMaXN0ZW5lciIsImtTdGF0dXNDb2RlIiwia1dlYlNvY2tldCIsIk5PT1AiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ws/lib/constants.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ws/lib/event-target.js":
/*!*********************************************!*\
  !*** ./node_modules/ws/lib/event-target.js ***!
  \*********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst { kForOnEventAttribute, kListener } = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/ws/lib/constants.js\");\nconst kCode = Symbol(\"kCode\");\nconst kData = Symbol(\"kData\");\nconst kError = Symbol(\"kError\");\nconst kMessage = Symbol(\"kMessage\");\nconst kReason = Symbol(\"kReason\");\nconst kTarget = Symbol(\"kTarget\");\nconst kType = Symbol(\"kType\");\nconst kWasClean = Symbol(\"kWasClean\");\n/**\n * Class representing an event.\n */ class Event {\n    /**\n   * Create a new `Event`.\n   *\n   * @param {String} type The name of the event\n   * @throws {TypeError} If the `type` argument is not specified\n   */ constructor(type){\n        this[kTarget] = null;\n        this[kType] = type;\n    }\n    /**\n   * @type {*}\n   */ get target() {\n        return this[kTarget];\n    }\n    /**\n   * @type {String}\n   */ get type() {\n        return this[kType];\n    }\n}\nObject.defineProperty(Event.prototype, \"target\", {\n    enumerable: true\n});\nObject.defineProperty(Event.prototype, \"type\", {\n    enumerable: true\n});\n/**\n * Class representing a close event.\n *\n * @extends Event\n */ class CloseEvent extends Event {\n    /**\n   * Create a new `CloseEvent`.\n   *\n   * @param {String} type The name of the event\n   * @param {Object} [options] A dictionary object that allows for setting\n   *     attributes via object members of the same name\n   * @param {Number} [options.code=0] The status code explaining why the\n   *     connection was closed\n   * @param {String} [options.reason=''] A human-readable string explaining why\n   *     the connection was closed\n   * @param {Boolean} [options.wasClean=false] Indicates whether or not the\n   *     connection was cleanly closed\n   */ constructor(type, options = {}){\n        super(type);\n        this[kCode] = options.code === undefined ? 0 : options.code;\n        this[kReason] = options.reason === undefined ? \"\" : options.reason;\n        this[kWasClean] = options.wasClean === undefined ? false : options.wasClean;\n    }\n    /**\n   * @type {Number}\n   */ get code() {\n        return this[kCode];\n    }\n    /**\n   * @type {String}\n   */ get reason() {\n        return this[kReason];\n    }\n    /**\n   * @type {Boolean}\n   */ get wasClean() {\n        return this[kWasClean];\n    }\n}\nObject.defineProperty(CloseEvent.prototype, \"code\", {\n    enumerable: true\n});\nObject.defineProperty(CloseEvent.prototype, \"reason\", {\n    enumerable: true\n});\nObject.defineProperty(CloseEvent.prototype, \"wasClean\", {\n    enumerable: true\n});\n/**\n * Class representing an error event.\n *\n * @extends Event\n */ class ErrorEvent extends Event {\n    /**\n   * Create a new `ErrorEvent`.\n   *\n   * @param {String} type The name of the event\n   * @param {Object} [options] A dictionary object that allows for setting\n   *     attributes via object members of the same name\n   * @param {*} [options.error=null] The error that generated this event\n   * @param {String} [options.message=''] The error message\n   */ constructor(type, options = {}){\n        super(type);\n        this[kError] = options.error === undefined ? null : options.error;\n        this[kMessage] = options.message === undefined ? \"\" : options.message;\n    }\n    /**\n   * @type {*}\n   */ get error() {\n        return this[kError];\n    }\n    /**\n   * @type {String}\n   */ get message() {\n        return this[kMessage];\n    }\n}\nObject.defineProperty(ErrorEvent.prototype, \"error\", {\n    enumerable: true\n});\nObject.defineProperty(ErrorEvent.prototype, \"message\", {\n    enumerable: true\n});\n/**\n * Class representing a message event.\n *\n * @extends Event\n */ class MessageEvent extends Event {\n    /**\n   * Create a new `MessageEvent`.\n   *\n   * @param {String} type The name of the event\n   * @param {Object} [options] A dictionary object that allows for setting\n   *     attributes via object members of the same name\n   * @param {*} [options.data=null] The message content\n   */ constructor(type, options = {}){\n        super(type);\n        this[kData] = options.data === undefined ? null : options.data;\n    }\n    /**\n   * @type {*}\n   */ get data() {\n        return this[kData];\n    }\n}\nObject.defineProperty(MessageEvent.prototype, \"data\", {\n    enumerable: true\n});\n/**\n * This provides methods for emulating the `EventTarget` interface. It's not\n * meant to be used directly.\n *\n * @mixin\n */ const EventTarget = {\n    /**\n   * Register an event listener.\n   *\n   * @param {String} type A string representing the event type to listen for\n   * @param {(Function|Object)} handler The listener to add\n   * @param {Object} [options] An options object specifies characteristics about\n   *     the event listener\n   * @param {Boolean} [options.once=false] A `Boolean` indicating that the\n   *     listener should be invoked at most once after being added. If `true`,\n   *     the listener would be automatically removed when invoked.\n   * @public\n   */ addEventListener (type, handler, options = {}) {\n        for (const listener of this.listeners(type)){\n            if (!options[kForOnEventAttribute] && listener[kListener] === handler && !listener[kForOnEventAttribute]) {\n                return;\n            }\n        }\n        let wrapper;\n        if (type === \"message\") {\n            wrapper = function onMessage(data, isBinary) {\n                const event = new MessageEvent(\"message\", {\n                    data: isBinary ? data : data.toString()\n                });\n                event[kTarget] = this;\n                callListener(handler, this, event);\n            };\n        } else if (type === \"close\") {\n            wrapper = function onClose(code, message) {\n                const event = new CloseEvent(\"close\", {\n                    code,\n                    reason: message.toString(),\n                    wasClean: this._closeFrameReceived && this._closeFrameSent\n                });\n                event[kTarget] = this;\n                callListener(handler, this, event);\n            };\n        } else if (type === \"error\") {\n            wrapper = function onError(error) {\n                const event = new ErrorEvent(\"error\", {\n                    error,\n                    message: error.message\n                });\n                event[kTarget] = this;\n                callListener(handler, this, event);\n            };\n        } else if (type === \"open\") {\n            wrapper = function onOpen() {\n                const event = new Event(\"open\");\n                event[kTarget] = this;\n                callListener(handler, this, event);\n            };\n        } else {\n            return;\n        }\n        wrapper[kForOnEventAttribute] = !!options[kForOnEventAttribute];\n        wrapper[kListener] = handler;\n        if (options.once) {\n            this.once(type, wrapper);\n        } else {\n            this.on(type, wrapper);\n        }\n    },\n    /**\n   * Remove an event listener.\n   *\n   * @param {String} type A string representing the event type to remove\n   * @param {(Function|Object)} handler The listener to remove\n   * @public\n   */ removeEventListener (type, handler) {\n        for (const listener of this.listeners(type)){\n            if (listener[kListener] === handler && !listener[kForOnEventAttribute]) {\n                this.removeListener(type, listener);\n                break;\n            }\n        }\n    }\n};\nmodule.exports = {\n    CloseEvent,\n    ErrorEvent,\n    Event,\n    EventTarget,\n    MessageEvent\n};\n/**\n * Call an event listener\n *\n * @param {(Function|Object)} listener The listener to call\n * @param {*} thisArg The value to use as `this`` when calling the listener\n * @param {Event} event The event to pass to the listener\n * @private\n */ function callListener(listener, thisArg, event) {\n    if (typeof listener === \"object\" && listener.handleEvent) {\n        listener.handleEvent.call(listener, event);\n    } else {\n        listener.call(thisArg, event);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ws/lib/event-target.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ws/lib/extension.js":
/*!******************************************!*\
  !*** ./node_modules/ws/lib/extension.js ***!
  \******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst { tokenChars } = __webpack_require__(/*! ./validation */ \"(ssr)/./node_modules/ws/lib/validation.js\");\n/**\n * Adds an offer to the map of extension offers or a parameter to the map of\n * parameters.\n *\n * @param {Object} dest The map of extension offers or parameters\n * @param {String} name The extension or parameter name\n * @param {(Object|Boolean|String)} elem The extension parameters or the\n *     parameter value\n * @private\n */ function push(dest, name, elem) {\n    if (dest[name] === undefined) dest[name] = [\n        elem\n    ];\n    else dest[name].push(elem);\n}\n/**\n * Parses the `Sec-WebSocket-Extensions` header into an object.\n *\n * @param {String} header The field value of the header\n * @return {Object} The parsed object\n * @public\n */ function parse(header) {\n    const offers = Object.create(null);\n    let params = Object.create(null);\n    let mustUnescape = false;\n    let isEscaping = false;\n    let inQuotes = false;\n    let extensionName;\n    let paramName;\n    let start = -1;\n    let code = -1;\n    let end = -1;\n    let i = 0;\n    for(; i < header.length; i++){\n        code = header.charCodeAt(i);\n        if (extensionName === undefined) {\n            if (end === -1 && tokenChars[code] === 1) {\n                if (start === -1) start = i;\n            } else if (i !== 0 && (code === 0x20 /* ' ' */  || code === 0x09)) {\n                if (end === -1 && start !== -1) end = i;\n            } else if (code === 0x3b /* ';' */  || code === 0x2c /* ',' */ ) {\n                if (start === -1) {\n                    throw new SyntaxError(`Unexpected character at index ${i}`);\n                }\n                if (end === -1) end = i;\n                const name = header.slice(start, end);\n                if (code === 0x2c) {\n                    push(offers, name, params);\n                    params = Object.create(null);\n                } else {\n                    extensionName = name;\n                }\n                start = end = -1;\n            } else {\n                throw new SyntaxError(`Unexpected character at index ${i}`);\n            }\n        } else if (paramName === undefined) {\n            if (end === -1 && tokenChars[code] === 1) {\n                if (start === -1) start = i;\n            } else if (code === 0x20 || code === 0x09) {\n                if (end === -1 && start !== -1) end = i;\n            } else if (code === 0x3b || code === 0x2c) {\n                if (start === -1) {\n                    throw new SyntaxError(`Unexpected character at index ${i}`);\n                }\n                if (end === -1) end = i;\n                push(params, header.slice(start, end), true);\n                if (code === 0x2c) {\n                    push(offers, extensionName, params);\n                    params = Object.create(null);\n                    extensionName = undefined;\n                }\n                start = end = -1;\n            } else if (code === 0x3d /* '=' */  && start !== -1 && end === -1) {\n                paramName = header.slice(start, i);\n                start = end = -1;\n            } else {\n                throw new SyntaxError(`Unexpected character at index ${i}`);\n            }\n        } else {\n            //\n            // The value of a quoted-string after unescaping must conform to the\n            // token ABNF, so only token characters are valid.\n            // Ref: https://tools.ietf.org/html/rfc6455#section-9.1\n            //\n            if (isEscaping) {\n                if (tokenChars[code] !== 1) {\n                    throw new SyntaxError(`Unexpected character at index ${i}`);\n                }\n                if (start === -1) start = i;\n                else if (!mustUnescape) mustUnescape = true;\n                isEscaping = false;\n            } else if (inQuotes) {\n                if (tokenChars[code] === 1) {\n                    if (start === -1) start = i;\n                } else if (code === 0x22 /* '\"' */  && start !== -1) {\n                    inQuotes = false;\n                    end = i;\n                } else if (code === 0x5c /* '\\' */ ) {\n                    isEscaping = true;\n                } else {\n                    throw new SyntaxError(`Unexpected character at index ${i}`);\n                }\n            } else if (code === 0x22 && header.charCodeAt(i - 1) === 0x3d) {\n                inQuotes = true;\n            } else if (end === -1 && tokenChars[code] === 1) {\n                if (start === -1) start = i;\n            } else if (start !== -1 && (code === 0x20 || code === 0x09)) {\n                if (end === -1) end = i;\n            } else if (code === 0x3b || code === 0x2c) {\n                if (start === -1) {\n                    throw new SyntaxError(`Unexpected character at index ${i}`);\n                }\n                if (end === -1) end = i;\n                let value = header.slice(start, end);\n                if (mustUnescape) {\n                    value = value.replace(/\\\\/g, \"\");\n                    mustUnescape = false;\n                }\n                push(params, paramName, value);\n                if (code === 0x2c) {\n                    push(offers, extensionName, params);\n                    params = Object.create(null);\n                    extensionName = undefined;\n                }\n                paramName = undefined;\n                start = end = -1;\n            } else {\n                throw new SyntaxError(`Unexpected character at index ${i}`);\n            }\n        }\n    }\n    if (start === -1 || inQuotes || code === 0x20 || code === 0x09) {\n        throw new SyntaxError(\"Unexpected end of input\");\n    }\n    if (end === -1) end = i;\n    const token = header.slice(start, end);\n    if (extensionName === undefined) {\n        push(offers, token, params);\n    } else {\n        if (paramName === undefined) {\n            push(params, token, true);\n        } else if (mustUnescape) {\n            push(params, paramName, token.replace(/\\\\/g, \"\"));\n        } else {\n            push(params, paramName, token);\n        }\n        push(offers, extensionName, params);\n    }\n    return offers;\n}\n/**\n * Builds the `Sec-WebSocket-Extensions` header field value.\n *\n * @param {Object} extensions The map of extensions and parameters to format\n * @return {String} A string representing the given object\n * @public\n */ function format(extensions) {\n    return Object.keys(extensions).map((extension)=>{\n        let configurations = extensions[extension];\n        if (!Array.isArray(configurations)) configurations = [\n            configurations\n        ];\n        return configurations.map((params)=>{\n            return [\n                extension\n            ].concat(Object.keys(params).map((k)=>{\n                let values = params[k];\n                if (!Array.isArray(values)) values = [\n                    values\n                ];\n                return values.map((v)=>v === true ? k : `${k}=${v}`).join(\"; \");\n            })).join(\"; \");\n        }).join(\", \");\n    }).join(\", \");\n}\nmodule.exports = {\n    format,\n    parse\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvd3MvbGliL2V4dGVuc2lvbi5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUVBLE1BQU0sRUFBRUEsVUFBVSxFQUFFLEdBQUdDLG1CQUFPQSxDQUFDO0FBRS9COzs7Ozs7Ozs7Q0FTQyxHQUNELFNBQVNDLEtBQUtDLElBQUksRUFBRUMsSUFBSSxFQUFFQyxJQUFJO0lBQzVCLElBQUlGLElBQUksQ0FBQ0MsS0FBSyxLQUFLRSxXQUFXSCxJQUFJLENBQUNDLEtBQUssR0FBRztRQUFDQztLQUFLO1NBQzVDRixJQUFJLENBQUNDLEtBQUssQ0FBQ0YsSUFBSSxDQUFDRztBQUN2QjtBQUVBOzs7Ozs7Q0FNQyxHQUNELFNBQVNFLE1BQU1DLE1BQU07SUFDbkIsTUFBTUMsU0FBU0MsT0FBT0MsTUFBTSxDQUFDO0lBQzdCLElBQUlDLFNBQVNGLE9BQU9DLE1BQU0sQ0FBQztJQUMzQixJQUFJRSxlQUFlO0lBQ25CLElBQUlDLGFBQWE7SUFDakIsSUFBSUMsV0FBVztJQUNmLElBQUlDO0lBQ0osSUFBSUM7SUFDSixJQUFJQyxRQUFRLENBQUM7SUFDYixJQUFJQyxPQUFPLENBQUM7SUFDWixJQUFJQyxNQUFNLENBQUM7SUFDWCxJQUFJQyxJQUFJO0lBRVIsTUFBT0EsSUFBSWIsT0FBT2MsTUFBTSxFQUFFRCxJQUFLO1FBQzdCRixPQUFPWCxPQUFPZSxVQUFVLENBQUNGO1FBRXpCLElBQUlMLGtCQUFrQlYsV0FBVztZQUMvQixJQUFJYyxRQUFRLENBQUMsS0FBS3BCLFVBQVUsQ0FBQ21CLEtBQUssS0FBSyxHQUFHO2dCQUN4QyxJQUFJRCxVQUFVLENBQUMsR0FBR0EsUUFBUUc7WUFDNUIsT0FBTyxJQUNMQSxNQUFNLEtBQ0xGLENBQUFBLFNBQVMsS0FBSyxPQUFPLE9BQU1BLFNBQVMsSUFBRyxHQUN4QztnQkFDQSxJQUFJQyxRQUFRLENBQUMsS0FBS0YsVUFBVSxDQUFDLEdBQUdFLE1BQU1DO1lBQ3hDLE9BQU8sSUFBSUYsU0FBUyxLQUFLLE9BQU8sT0FBTUEsU0FBUyxLQUFLLE9BQU8sS0FBSTtnQkFDN0QsSUFBSUQsVUFBVSxDQUFDLEdBQUc7b0JBQ2hCLE1BQU0sSUFBSU0sWUFBWSxDQUFDLDhCQUE4QixFQUFFSCxFQUFFLENBQUM7Z0JBQzVEO2dCQUVBLElBQUlELFFBQVEsQ0FBQyxHQUFHQSxNQUFNQztnQkFDdEIsTUFBTWpCLE9BQU9JLE9BQU9pQixLQUFLLENBQUNQLE9BQU9FO2dCQUNqQyxJQUFJRCxTQUFTLE1BQU07b0JBQ2pCakIsS0FBS08sUUFBUUwsTUFBTVE7b0JBQ25CQSxTQUFTRixPQUFPQyxNQUFNLENBQUM7Z0JBQ3pCLE9BQU87b0JBQ0xLLGdCQUFnQlo7Z0JBQ2xCO2dCQUVBYyxRQUFRRSxNQUFNLENBQUM7WUFDakIsT0FBTztnQkFDTCxNQUFNLElBQUlJLFlBQVksQ0FBQyw4QkFBOEIsRUFBRUgsRUFBRSxDQUFDO1lBQzVEO1FBQ0YsT0FBTyxJQUFJSixjQUFjWCxXQUFXO1lBQ2xDLElBQUljLFFBQVEsQ0FBQyxLQUFLcEIsVUFBVSxDQUFDbUIsS0FBSyxLQUFLLEdBQUc7Z0JBQ3hDLElBQUlELFVBQVUsQ0FBQyxHQUFHQSxRQUFRRztZQUM1QixPQUFPLElBQUlGLFNBQVMsUUFBUUEsU0FBUyxNQUFNO2dCQUN6QyxJQUFJQyxRQUFRLENBQUMsS0FBS0YsVUFBVSxDQUFDLEdBQUdFLE1BQU1DO1lBQ3hDLE9BQU8sSUFBSUYsU0FBUyxRQUFRQSxTQUFTLE1BQU07Z0JBQ3pDLElBQUlELFVBQVUsQ0FBQyxHQUFHO29CQUNoQixNQUFNLElBQUlNLFlBQVksQ0FBQyw4QkFBOEIsRUFBRUgsRUFBRSxDQUFDO2dCQUM1RDtnQkFFQSxJQUFJRCxRQUFRLENBQUMsR0FBR0EsTUFBTUM7Z0JBQ3RCbkIsS0FBS1UsUUFBUUosT0FBT2lCLEtBQUssQ0FBQ1AsT0FBT0UsTUFBTTtnQkFDdkMsSUFBSUQsU0FBUyxNQUFNO29CQUNqQmpCLEtBQUtPLFFBQVFPLGVBQWVKO29CQUM1QkEsU0FBU0YsT0FBT0MsTUFBTSxDQUFDO29CQUN2QkssZ0JBQWdCVjtnQkFDbEI7Z0JBRUFZLFFBQVFFLE1BQU0sQ0FBQztZQUNqQixPQUFPLElBQUlELFNBQVMsS0FBSyxPQUFPLE9BQU1ELFVBQVUsQ0FBQyxLQUFLRSxRQUFRLENBQUMsR0FBRztnQkFDaEVILFlBQVlULE9BQU9pQixLQUFLLENBQUNQLE9BQU9HO2dCQUNoQ0gsUUFBUUUsTUFBTSxDQUFDO1lBQ2pCLE9BQU87Z0JBQ0wsTUFBTSxJQUFJSSxZQUFZLENBQUMsOEJBQThCLEVBQUVILEVBQUUsQ0FBQztZQUM1RDtRQUNGLE9BQU87WUFDTCxFQUFFO1lBQ0Ysb0VBQW9FO1lBQ3BFLGtEQUFrRDtZQUNsRCx1REFBdUQ7WUFDdkQsRUFBRTtZQUNGLElBQUlQLFlBQVk7Z0JBQ2QsSUFBSWQsVUFBVSxDQUFDbUIsS0FBSyxLQUFLLEdBQUc7b0JBQzFCLE1BQU0sSUFBSUssWUFBWSxDQUFDLDhCQUE4QixFQUFFSCxFQUFFLENBQUM7Z0JBQzVEO2dCQUNBLElBQUlILFVBQVUsQ0FBQyxHQUFHQSxRQUFRRztxQkFDckIsSUFBSSxDQUFDUixjQUFjQSxlQUFlO2dCQUN2Q0MsYUFBYTtZQUNmLE9BQU8sSUFBSUMsVUFBVTtnQkFDbkIsSUFBSWYsVUFBVSxDQUFDbUIsS0FBSyxLQUFLLEdBQUc7b0JBQzFCLElBQUlELFVBQVUsQ0FBQyxHQUFHQSxRQUFRRztnQkFDNUIsT0FBTyxJQUFJRixTQUFTLEtBQUssT0FBTyxPQUFNRCxVQUFVLENBQUMsR0FBRztvQkFDbERILFdBQVc7b0JBQ1hLLE1BQU1DO2dCQUNSLE9BQU8sSUFBSUYsU0FBUyxLQUFLLE9BQU8sS0FBSTtvQkFDbENMLGFBQWE7Z0JBQ2YsT0FBTztvQkFDTCxNQUFNLElBQUlVLFlBQVksQ0FBQyw4QkFBOEIsRUFBRUgsRUFBRSxDQUFDO2dCQUM1RDtZQUNGLE9BQU8sSUFBSUYsU0FBUyxRQUFRWCxPQUFPZSxVQUFVLENBQUNGLElBQUksT0FBTyxNQUFNO2dCQUM3RE4sV0FBVztZQUNiLE9BQU8sSUFBSUssUUFBUSxDQUFDLEtBQUtwQixVQUFVLENBQUNtQixLQUFLLEtBQUssR0FBRztnQkFDL0MsSUFBSUQsVUFBVSxDQUFDLEdBQUdBLFFBQVFHO1lBQzVCLE9BQU8sSUFBSUgsVUFBVSxDQUFDLEtBQU1DLENBQUFBLFNBQVMsUUFBUUEsU0FBUyxJQUFHLEdBQUk7Z0JBQzNELElBQUlDLFFBQVEsQ0FBQyxHQUFHQSxNQUFNQztZQUN4QixPQUFPLElBQUlGLFNBQVMsUUFBUUEsU0FBUyxNQUFNO2dCQUN6QyxJQUFJRCxVQUFVLENBQUMsR0FBRztvQkFDaEIsTUFBTSxJQUFJTSxZQUFZLENBQUMsOEJBQThCLEVBQUVILEVBQUUsQ0FBQztnQkFDNUQ7Z0JBRUEsSUFBSUQsUUFBUSxDQUFDLEdBQUdBLE1BQU1DO2dCQUN0QixJQUFJSyxRQUFRbEIsT0FBT2lCLEtBQUssQ0FBQ1AsT0FBT0U7Z0JBQ2hDLElBQUlQLGNBQWM7b0JBQ2hCYSxRQUFRQSxNQUFNQyxPQUFPLENBQUMsT0FBTztvQkFDN0JkLGVBQWU7Z0JBQ2pCO2dCQUNBWCxLQUFLVSxRQUFRSyxXQUFXUztnQkFDeEIsSUFBSVAsU0FBUyxNQUFNO29CQUNqQmpCLEtBQUtPLFFBQVFPLGVBQWVKO29CQUM1QkEsU0FBU0YsT0FBT0MsTUFBTSxDQUFDO29CQUN2QkssZ0JBQWdCVjtnQkFDbEI7Z0JBRUFXLFlBQVlYO2dCQUNaWSxRQUFRRSxNQUFNLENBQUM7WUFDakIsT0FBTztnQkFDTCxNQUFNLElBQUlJLFlBQVksQ0FBQyw4QkFBOEIsRUFBRUgsRUFBRSxDQUFDO1lBQzVEO1FBQ0Y7SUFDRjtJQUVBLElBQUlILFVBQVUsQ0FBQyxLQUFLSCxZQUFZSSxTQUFTLFFBQVFBLFNBQVMsTUFBTTtRQUM5RCxNQUFNLElBQUlLLFlBQVk7SUFDeEI7SUFFQSxJQUFJSixRQUFRLENBQUMsR0FBR0EsTUFBTUM7SUFDdEIsTUFBTU8sUUFBUXBCLE9BQU9pQixLQUFLLENBQUNQLE9BQU9FO0lBQ2xDLElBQUlKLGtCQUFrQlYsV0FBVztRQUMvQkosS0FBS08sUUFBUW1CLE9BQU9oQjtJQUN0QixPQUFPO1FBQ0wsSUFBSUssY0FBY1gsV0FBVztZQUMzQkosS0FBS1UsUUFBUWdCLE9BQU87UUFDdEIsT0FBTyxJQUFJZixjQUFjO1lBQ3ZCWCxLQUFLVSxRQUFRSyxXQUFXVyxNQUFNRCxPQUFPLENBQUMsT0FBTztRQUMvQyxPQUFPO1lBQ0x6QixLQUFLVSxRQUFRSyxXQUFXVztRQUMxQjtRQUNBMUIsS0FBS08sUUFBUU8sZUFBZUo7SUFDOUI7SUFFQSxPQUFPSDtBQUNUO0FBRUE7Ozs7OztDQU1DLEdBQ0QsU0FBU29CLE9BQU9DLFVBQVU7SUFDeEIsT0FBT3BCLE9BQU9xQixJQUFJLENBQUNELFlBQ2hCRSxHQUFHLENBQUMsQ0FBQ0M7UUFDSixJQUFJQyxpQkFBaUJKLFVBQVUsQ0FBQ0csVUFBVTtRQUMxQyxJQUFJLENBQUNFLE1BQU1DLE9BQU8sQ0FBQ0YsaUJBQWlCQSxpQkFBaUI7WUFBQ0E7U0FBZTtRQUNyRSxPQUFPQSxlQUNKRixHQUFHLENBQUMsQ0FBQ3BCO1lBQ0osT0FBTztnQkFBQ3FCO2FBQVUsQ0FDZkksTUFBTSxDQUNMM0IsT0FBT3FCLElBQUksQ0FBQ25CLFFBQVFvQixHQUFHLENBQUMsQ0FBQ007Z0JBQ3ZCLElBQUlDLFNBQVMzQixNQUFNLENBQUMwQixFQUFFO2dCQUN0QixJQUFJLENBQUNILE1BQU1DLE9BQU8sQ0FBQ0csU0FBU0EsU0FBUztvQkFBQ0E7aUJBQU87Z0JBQzdDLE9BQU9BLE9BQ0pQLEdBQUcsQ0FBQyxDQUFDUSxJQUFPQSxNQUFNLE9BQU9GLElBQUksQ0FBQyxFQUFFQSxFQUFFLENBQUMsRUFBRUUsRUFBRSxDQUFDLEVBQ3hDQyxJQUFJLENBQUM7WUFDVixJQUVEQSxJQUFJLENBQUM7UUFDVixHQUNDQSxJQUFJLENBQUM7SUFDVixHQUNDQSxJQUFJLENBQUM7QUFDVjtBQUVBQyxPQUFPQyxPQUFPLEdBQUc7SUFBRWQ7SUFBUXRCO0FBQU0iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9iaGVlbWRpbmUvLi9ub2RlX21vZHVsZXMvd3MvbGliL2V4dGVuc2lvbi5qcz9hMzJlIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuY29uc3QgeyB0b2tlbkNoYXJzIH0gPSByZXF1aXJlKCcuL3ZhbGlkYXRpb24nKTtcblxuLyoqXG4gKiBBZGRzIGFuIG9mZmVyIHRvIHRoZSBtYXAgb2YgZXh0ZW5zaW9uIG9mZmVycyBvciBhIHBhcmFtZXRlciB0byB0aGUgbWFwIG9mXG4gKiBwYXJhbWV0ZXJzLlxuICpcbiAqIEBwYXJhbSB7T2JqZWN0fSBkZXN0IFRoZSBtYXAgb2YgZXh0ZW5zaW9uIG9mZmVycyBvciBwYXJhbWV0ZXJzXG4gKiBAcGFyYW0ge1N0cmluZ30gbmFtZSBUaGUgZXh0ZW5zaW9uIG9yIHBhcmFtZXRlciBuYW1lXG4gKiBAcGFyYW0geyhPYmplY3R8Qm9vbGVhbnxTdHJpbmcpfSBlbGVtIFRoZSBleHRlbnNpb24gcGFyYW1ldGVycyBvciB0aGVcbiAqICAgICBwYXJhbWV0ZXIgdmFsdWVcbiAqIEBwcml2YXRlXG4gKi9cbmZ1bmN0aW9uIHB1c2goZGVzdCwgbmFtZSwgZWxlbSkge1xuICBpZiAoZGVzdFtuYW1lXSA9PT0gdW5kZWZpbmVkKSBkZXN0W25hbWVdID0gW2VsZW1dO1xuICBlbHNlIGRlc3RbbmFtZV0ucHVzaChlbGVtKTtcbn1cblxuLyoqXG4gKiBQYXJzZXMgdGhlIGBTZWMtV2ViU29ja2V0LUV4dGVuc2lvbnNgIGhlYWRlciBpbnRvIGFuIG9iamVjdC5cbiAqXG4gKiBAcGFyYW0ge1N0cmluZ30gaGVhZGVyIFRoZSBmaWVsZCB2YWx1ZSBvZiB0aGUgaGVhZGVyXG4gKiBAcmV0dXJuIHtPYmplY3R9IFRoZSBwYXJzZWQgb2JqZWN0XG4gKiBAcHVibGljXG4gKi9cbmZ1bmN0aW9uIHBhcnNlKGhlYWRlcikge1xuICBjb25zdCBvZmZlcnMgPSBPYmplY3QuY3JlYXRlKG51bGwpO1xuICBsZXQgcGFyYW1zID0gT2JqZWN0LmNyZWF0ZShudWxsKTtcbiAgbGV0IG11c3RVbmVzY2FwZSA9IGZhbHNlO1xuICBsZXQgaXNFc2NhcGluZyA9IGZhbHNlO1xuICBsZXQgaW5RdW90ZXMgPSBmYWxzZTtcbiAgbGV0IGV4dGVuc2lvbk5hbWU7XG4gIGxldCBwYXJhbU5hbWU7XG4gIGxldCBzdGFydCA9IC0xO1xuICBsZXQgY29kZSA9IC0xO1xuICBsZXQgZW5kID0gLTE7XG4gIGxldCBpID0gMDtcblxuICBmb3IgKDsgaSA8IGhlYWRlci5sZW5ndGg7IGkrKykge1xuICAgIGNvZGUgPSBoZWFkZXIuY2hhckNvZGVBdChpKTtcblxuICAgIGlmIChleHRlbnNpb25OYW1lID09PSB1bmRlZmluZWQpIHtcbiAgICAgIGlmIChlbmQgPT09IC0xICYmIHRva2VuQ2hhcnNbY29kZV0gPT09IDEpIHtcbiAgICAgICAgaWYgKHN0YXJ0ID09PSAtMSkgc3RhcnQgPSBpO1xuICAgICAgfSBlbHNlIGlmIChcbiAgICAgICAgaSAhPT0gMCAmJlxuICAgICAgICAoY29kZSA9PT0gMHgyMCAvKiAnICcgKi8gfHwgY29kZSA9PT0gMHgwOSkgLyogJ1xcdCcgKi9cbiAgICAgICkge1xuICAgICAgICBpZiAoZW5kID09PSAtMSAmJiBzdGFydCAhPT0gLTEpIGVuZCA9IGk7XG4gICAgICB9IGVsc2UgaWYgKGNvZGUgPT09IDB4M2IgLyogJzsnICovIHx8IGNvZGUgPT09IDB4MmMgLyogJywnICovKSB7XG4gICAgICAgIGlmIChzdGFydCA9PT0gLTEpIHtcbiAgICAgICAgICB0aHJvdyBuZXcgU3ludGF4RXJyb3IoYFVuZXhwZWN0ZWQgY2hhcmFjdGVyIGF0IGluZGV4ICR7aX1gKTtcbiAgICAgICAgfVxuXG4gICAgICAgIGlmIChlbmQgPT09IC0xKSBlbmQgPSBpO1xuICAgICAgICBjb25zdCBuYW1lID0gaGVhZGVyLnNsaWNlKHN0YXJ0LCBlbmQpO1xuICAgICAgICBpZiAoY29kZSA9PT0gMHgyYykge1xuICAgICAgICAgIHB1c2gob2ZmZXJzLCBuYW1lLCBwYXJhbXMpO1xuICAgICAgICAgIHBhcmFtcyA9IE9iamVjdC5jcmVhdGUobnVsbCk7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgZXh0ZW5zaW9uTmFtZSA9IG5hbWU7XG4gICAgICAgIH1cblxuICAgICAgICBzdGFydCA9IGVuZCA9IC0xO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgdGhyb3cgbmV3IFN5bnRheEVycm9yKGBVbmV4cGVjdGVkIGNoYXJhY3RlciBhdCBpbmRleCAke2l9YCk7XG4gICAgICB9XG4gICAgfSBlbHNlIGlmIChwYXJhbU5hbWUgPT09IHVuZGVmaW5lZCkge1xuICAgICAgaWYgKGVuZCA9PT0gLTEgJiYgdG9rZW5DaGFyc1tjb2RlXSA9PT0gMSkge1xuICAgICAgICBpZiAoc3RhcnQgPT09IC0xKSBzdGFydCA9IGk7XG4gICAgICB9IGVsc2UgaWYgKGNvZGUgPT09IDB4MjAgfHwgY29kZSA9PT0gMHgwOSkge1xuICAgICAgICBpZiAoZW5kID09PSAtMSAmJiBzdGFydCAhPT0gLTEpIGVuZCA9IGk7XG4gICAgICB9IGVsc2UgaWYgKGNvZGUgPT09IDB4M2IgfHwgY29kZSA9PT0gMHgyYykge1xuICAgICAgICBpZiAoc3RhcnQgPT09IC0xKSB7XG4gICAgICAgICAgdGhyb3cgbmV3IFN5bnRheEVycm9yKGBVbmV4cGVjdGVkIGNoYXJhY3RlciBhdCBpbmRleCAke2l9YCk7XG4gICAgICAgIH1cblxuICAgICAgICBpZiAoZW5kID09PSAtMSkgZW5kID0gaTtcbiAgICAgICAgcHVzaChwYXJhbXMsIGhlYWRlci5zbGljZShzdGFydCwgZW5kKSwgdHJ1ZSk7XG4gICAgICAgIGlmIChjb2RlID09PSAweDJjKSB7XG4gICAgICAgICAgcHVzaChvZmZlcnMsIGV4dGVuc2lvbk5hbWUsIHBhcmFtcyk7XG4gICAgICAgICAgcGFyYW1zID0gT2JqZWN0LmNyZWF0ZShudWxsKTtcbiAgICAgICAgICBleHRlbnNpb25OYW1lID0gdW5kZWZpbmVkO1xuICAgICAgICB9XG5cbiAgICAgICAgc3RhcnQgPSBlbmQgPSAtMTtcbiAgICAgIH0gZWxzZSBpZiAoY29kZSA9PT0gMHgzZCAvKiAnPScgKi8gJiYgc3RhcnQgIT09IC0xICYmIGVuZCA9PT0gLTEpIHtcbiAgICAgICAgcGFyYW1OYW1lID0gaGVhZGVyLnNsaWNlKHN0YXJ0LCBpKTtcbiAgICAgICAgc3RhcnQgPSBlbmQgPSAtMTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHRocm93IG5ldyBTeW50YXhFcnJvcihgVW5leHBlY3RlZCBjaGFyYWN0ZXIgYXQgaW5kZXggJHtpfWApO1xuICAgICAgfVxuICAgIH0gZWxzZSB7XG4gICAgICAvL1xuICAgICAgLy8gVGhlIHZhbHVlIG9mIGEgcXVvdGVkLXN0cmluZyBhZnRlciB1bmVzY2FwaW5nIG11c3QgY29uZm9ybSB0byB0aGVcbiAgICAgIC8vIHRva2VuIEFCTkYsIHNvIG9ubHkgdG9rZW4gY2hhcmFjdGVycyBhcmUgdmFsaWQuXG4gICAgICAvLyBSZWY6IGh0dHBzOi8vdG9vbHMuaWV0Zi5vcmcvaHRtbC9yZmM2NDU1I3NlY3Rpb24tOS4xXG4gICAgICAvL1xuICAgICAgaWYgKGlzRXNjYXBpbmcpIHtcbiAgICAgICAgaWYgKHRva2VuQ2hhcnNbY29kZV0gIT09IDEpIHtcbiAgICAgICAgICB0aHJvdyBuZXcgU3ludGF4RXJyb3IoYFVuZXhwZWN0ZWQgY2hhcmFjdGVyIGF0IGluZGV4ICR7aX1gKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAoc3RhcnQgPT09IC0xKSBzdGFydCA9IGk7XG4gICAgICAgIGVsc2UgaWYgKCFtdXN0VW5lc2NhcGUpIG11c3RVbmVzY2FwZSA9IHRydWU7XG4gICAgICAgIGlzRXNjYXBpbmcgPSBmYWxzZTtcbiAgICAgIH0gZWxzZSBpZiAoaW5RdW90ZXMpIHtcbiAgICAgICAgaWYgKHRva2VuQ2hhcnNbY29kZV0gPT09IDEpIHtcbiAgICAgICAgICBpZiAoc3RhcnQgPT09IC0xKSBzdGFydCA9IGk7XG4gICAgICAgIH0gZWxzZSBpZiAoY29kZSA9PT0gMHgyMiAvKiAnXCInICovICYmIHN0YXJ0ICE9PSAtMSkge1xuICAgICAgICAgIGluUXVvdGVzID0gZmFsc2U7XG4gICAgICAgICAgZW5kID0gaTtcbiAgICAgICAgfSBlbHNlIGlmIChjb2RlID09PSAweDVjIC8qICdcXCcgKi8pIHtcbiAgICAgICAgICBpc0VzY2FwaW5nID0gdHJ1ZTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICB0aHJvdyBuZXcgU3ludGF4RXJyb3IoYFVuZXhwZWN0ZWQgY2hhcmFjdGVyIGF0IGluZGV4ICR7aX1gKTtcbiAgICAgICAgfVxuICAgICAgfSBlbHNlIGlmIChjb2RlID09PSAweDIyICYmIGhlYWRlci5jaGFyQ29kZUF0KGkgLSAxKSA9PT0gMHgzZCkge1xuICAgICAgICBpblF1b3RlcyA9IHRydWU7XG4gICAgICB9IGVsc2UgaWYgKGVuZCA9PT0gLTEgJiYgdG9rZW5DaGFyc1tjb2RlXSA9PT0gMSkge1xuICAgICAgICBpZiAoc3RhcnQgPT09IC0xKSBzdGFydCA9IGk7XG4gICAgICB9IGVsc2UgaWYgKHN0YXJ0ICE9PSAtMSAmJiAoY29kZSA9PT0gMHgyMCB8fCBjb2RlID09PSAweDA5KSkge1xuICAgICAgICBpZiAoZW5kID09PSAtMSkgZW5kID0gaTtcbiAgICAgIH0gZWxzZSBpZiAoY29kZSA9PT0gMHgzYiB8fCBjb2RlID09PSAweDJjKSB7XG4gICAgICAgIGlmIChzdGFydCA9PT0gLTEpIHtcbiAgICAgICAgICB0aHJvdyBuZXcgU3ludGF4RXJyb3IoYFVuZXhwZWN0ZWQgY2hhcmFjdGVyIGF0IGluZGV4ICR7aX1gKTtcbiAgICAgICAgfVxuXG4gICAgICAgIGlmIChlbmQgPT09IC0xKSBlbmQgPSBpO1xuICAgICAgICBsZXQgdmFsdWUgPSBoZWFkZXIuc2xpY2Uoc3RhcnQsIGVuZCk7XG4gICAgICAgIGlmIChtdXN0VW5lc2NhcGUpIHtcbiAgICAgICAgICB2YWx1ZSA9IHZhbHVlLnJlcGxhY2UoL1xcXFwvZywgJycpO1xuICAgICAgICAgIG11c3RVbmVzY2FwZSA9IGZhbHNlO1xuICAgICAgICB9XG4gICAgICAgIHB1c2gocGFyYW1zLCBwYXJhbU5hbWUsIHZhbHVlKTtcbiAgICAgICAgaWYgKGNvZGUgPT09IDB4MmMpIHtcbiAgICAgICAgICBwdXNoKG9mZmVycywgZXh0ZW5zaW9uTmFtZSwgcGFyYW1zKTtcbiAgICAgICAgICBwYXJhbXMgPSBPYmplY3QuY3JlYXRlKG51bGwpO1xuICAgICAgICAgIGV4dGVuc2lvbk5hbWUgPSB1bmRlZmluZWQ7XG4gICAgICAgIH1cblxuICAgICAgICBwYXJhbU5hbWUgPSB1bmRlZmluZWQ7XG4gICAgICAgIHN0YXJ0ID0gZW5kID0gLTE7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICB0aHJvdyBuZXcgU3ludGF4RXJyb3IoYFVuZXhwZWN0ZWQgY2hhcmFjdGVyIGF0IGluZGV4ICR7aX1gKTtcbiAgICAgIH1cbiAgICB9XG4gIH1cblxuICBpZiAoc3RhcnQgPT09IC0xIHx8IGluUXVvdGVzIHx8IGNvZGUgPT09IDB4MjAgfHwgY29kZSA9PT0gMHgwOSkge1xuICAgIHRocm93IG5ldyBTeW50YXhFcnJvcignVW5leHBlY3RlZCBlbmQgb2YgaW5wdXQnKTtcbiAgfVxuXG4gIGlmIChlbmQgPT09IC0xKSBlbmQgPSBpO1xuICBjb25zdCB0b2tlbiA9IGhlYWRlci5zbGljZShzdGFydCwgZW5kKTtcbiAgaWYgKGV4dGVuc2lvbk5hbWUgPT09IHVuZGVmaW5lZCkge1xuICAgIHB1c2gob2ZmZXJzLCB0b2tlbiwgcGFyYW1zKTtcbiAgfSBlbHNlIHtcbiAgICBpZiAocGFyYW1OYW1lID09PSB1bmRlZmluZWQpIHtcbiAgICAgIHB1c2gocGFyYW1zLCB0b2tlbiwgdHJ1ZSk7XG4gICAgfSBlbHNlIGlmIChtdXN0VW5lc2NhcGUpIHtcbiAgICAgIHB1c2gocGFyYW1zLCBwYXJhbU5hbWUsIHRva2VuLnJlcGxhY2UoL1xcXFwvZywgJycpKTtcbiAgICB9IGVsc2Uge1xuICAgICAgcHVzaChwYXJhbXMsIHBhcmFtTmFtZSwgdG9rZW4pO1xuICAgIH1cbiAgICBwdXNoKG9mZmVycywgZXh0ZW5zaW9uTmFtZSwgcGFyYW1zKTtcbiAgfVxuXG4gIHJldHVybiBvZmZlcnM7XG59XG5cbi8qKlxuICogQnVpbGRzIHRoZSBgU2VjLVdlYlNvY2tldC1FeHRlbnNpb25zYCBoZWFkZXIgZmllbGQgdmFsdWUuXG4gKlxuICogQHBhcmFtIHtPYmplY3R9IGV4dGVuc2lvbnMgVGhlIG1hcCBvZiBleHRlbnNpb25zIGFuZCBwYXJhbWV0ZXJzIHRvIGZvcm1hdFxuICogQHJldHVybiB7U3RyaW5nfSBBIHN0cmluZyByZXByZXNlbnRpbmcgdGhlIGdpdmVuIG9iamVjdFxuICogQHB1YmxpY1xuICovXG5mdW5jdGlvbiBmb3JtYXQoZXh0ZW5zaW9ucykge1xuICByZXR1cm4gT2JqZWN0LmtleXMoZXh0ZW5zaW9ucylcbiAgICAubWFwKChleHRlbnNpb24pID0+IHtcbiAgICAgIGxldCBjb25maWd1cmF0aW9ucyA9IGV4dGVuc2lvbnNbZXh0ZW5zaW9uXTtcbiAgICAgIGlmICghQXJyYXkuaXNBcnJheShjb25maWd1cmF0aW9ucykpIGNvbmZpZ3VyYXRpb25zID0gW2NvbmZpZ3VyYXRpb25zXTtcbiAgICAgIHJldHVybiBjb25maWd1cmF0aW9uc1xuICAgICAgICAubWFwKChwYXJhbXMpID0+IHtcbiAgICAgICAgICByZXR1cm4gW2V4dGVuc2lvbl1cbiAgICAgICAgICAgIC5jb25jYXQoXG4gICAgICAgICAgICAgIE9iamVjdC5rZXlzKHBhcmFtcykubWFwKChrKSA9PiB7XG4gICAgICAgICAgICAgICAgbGV0IHZhbHVlcyA9IHBhcmFtc1trXTtcbiAgICAgICAgICAgICAgICBpZiAoIUFycmF5LmlzQXJyYXkodmFsdWVzKSkgdmFsdWVzID0gW3ZhbHVlc107XG4gICAgICAgICAgICAgICAgcmV0dXJuIHZhbHVlc1xuICAgICAgICAgICAgICAgICAgLm1hcCgodikgPT4gKHYgPT09IHRydWUgPyBrIDogYCR7a309JHt2fWApKVxuICAgICAgICAgICAgICAgICAgLmpvaW4oJzsgJyk7XG4gICAgICAgICAgICAgIH0pXG4gICAgICAgICAgICApXG4gICAgICAgICAgICAuam9pbignOyAnKTtcbiAgICAgICAgfSlcbiAgICAgICAgLmpvaW4oJywgJyk7XG4gICAgfSlcbiAgICAuam9pbignLCAnKTtcbn1cblxubW9kdWxlLmV4cG9ydHMgPSB7IGZvcm1hdCwgcGFyc2UgfTtcbiJdLCJuYW1lcyI6WyJ0b2tlbkNoYXJzIiwicmVxdWlyZSIsInB1c2giLCJkZXN0IiwibmFtZSIsImVsZW0iLCJ1bmRlZmluZWQiLCJwYXJzZSIsImhlYWRlciIsIm9mZmVycyIsIk9iamVjdCIsImNyZWF0ZSIsInBhcmFtcyIsIm11c3RVbmVzY2FwZSIsImlzRXNjYXBpbmciLCJpblF1b3RlcyIsImV4dGVuc2lvbk5hbWUiLCJwYXJhbU5hbWUiLCJzdGFydCIsImNvZGUiLCJlbmQiLCJpIiwibGVuZ3RoIiwiY2hhckNvZGVBdCIsIlN5bnRheEVycm9yIiwic2xpY2UiLCJ2YWx1ZSIsInJlcGxhY2UiLCJ0b2tlbiIsImZvcm1hdCIsImV4dGVuc2lvbnMiLCJrZXlzIiwibWFwIiwiZXh0ZW5zaW9uIiwiY29uZmlndXJhdGlvbnMiLCJBcnJheSIsImlzQXJyYXkiLCJjb25jYXQiLCJrIiwidmFsdWVzIiwidiIsImpvaW4iLCJtb2R1bGUiLCJleHBvcnRzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ws/lib/extension.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ws/lib/limiter.js":
/*!****************************************!*\
  !*** ./node_modules/ws/lib/limiter.js ***!
  \****************************************/
/***/ ((module) => {

eval("\nconst kDone = Symbol(\"kDone\");\nconst kRun = Symbol(\"kRun\");\n/**\n * A very simple job queue with adjustable concurrency. Adapted from\n * https://github.com/STRML/async-limiter\n */ class Limiter {\n    /**\n   * Creates a new `Limiter`.\n   *\n   * @param {Number} [concurrency=Infinity] The maximum number of jobs allowed\n   *     to run concurrently\n   */ constructor(concurrency){\n        this[kDone] = ()=>{\n            this.pending--;\n            this[kRun]();\n        };\n        this.concurrency = concurrency || Infinity;\n        this.jobs = [];\n        this.pending = 0;\n    }\n    /**\n   * Adds a job to the queue.\n   *\n   * @param {Function} job The job to run\n   * @public\n   */ add(job) {\n        this.jobs.push(job);\n        this[kRun]();\n    }\n    /**\n   * Removes a job from the queue and runs it if possible.\n   *\n   * @private\n   */ [kRun]() {\n        if (this.pending === this.concurrency) return;\n        if (this.jobs.length) {\n            const job = this.jobs.shift();\n            this.pending++;\n            job(this[kDone]);\n        }\n    }\n}\nmodule.exports = Limiter;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ws/lib/limiter.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ws/lib/permessage-deflate.js":
/*!***************************************************!*\
  !*** ./node_modules/ws/lib/permessage-deflate.js ***!
  \***************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst zlib = __webpack_require__(/*! zlib */ \"zlib\");\nconst bufferUtil = __webpack_require__(/*! ./buffer-util */ \"(ssr)/./node_modules/ws/lib/buffer-util.js\");\nconst Limiter = __webpack_require__(/*! ./limiter */ \"(ssr)/./node_modules/ws/lib/limiter.js\");\nconst { kStatusCode } = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/ws/lib/constants.js\");\nconst FastBuffer = Buffer[Symbol.species];\nconst TRAILER = Buffer.from([\n    0x00,\n    0x00,\n    0xff,\n    0xff\n]);\nconst kPerMessageDeflate = Symbol(\"permessage-deflate\");\nconst kTotalLength = Symbol(\"total-length\");\nconst kCallback = Symbol(\"callback\");\nconst kBuffers = Symbol(\"buffers\");\nconst kError = Symbol(\"error\");\n//\n// We limit zlib concurrency, which prevents severe memory fragmentation\n// as documented in https://github.com/nodejs/node/issues/8871#issuecomment-250915913\n// and https://github.com/websockets/ws/issues/1202\n//\n// Intentionally global; it's the global thread pool that's an issue.\n//\nlet zlibLimiter;\n/**\n * permessage-deflate implementation.\n */ class PerMessageDeflate {\n    /**\n   * Creates a PerMessageDeflate instance.\n   *\n   * @param {Object} [options] Configuration options\n   * @param {(Boolean|Number)} [options.clientMaxWindowBits] Advertise support\n   *     for, or request, a custom client window size\n   * @param {Boolean} [options.clientNoContextTakeover=false] Advertise/\n   *     acknowledge disabling of client context takeover\n   * @param {Number} [options.concurrencyLimit=10] The number of concurrent\n   *     calls to zlib\n   * @param {(Boolean|Number)} [options.serverMaxWindowBits] Request/confirm the\n   *     use of a custom server window size\n   * @param {Boolean} [options.serverNoContextTakeover=false] Request/accept\n   *     disabling of server context takeover\n   * @param {Number} [options.threshold=1024] Size (in bytes) below which\n   *     messages should not be compressed if context takeover is disabled\n   * @param {Object} [options.zlibDeflateOptions] Options to pass to zlib on\n   *     deflate\n   * @param {Object} [options.zlibInflateOptions] Options to pass to zlib on\n   *     inflate\n   * @param {Boolean} [isServer=false] Create the instance in either server or\n   *     client mode\n   * @param {Number} [maxPayload=0] The maximum allowed message length\n   */ constructor(options, isServer, maxPayload){\n        this._maxPayload = maxPayload | 0;\n        this._options = options || {};\n        this._threshold = this._options.threshold !== undefined ? this._options.threshold : 1024;\n        this._isServer = !!isServer;\n        this._deflate = null;\n        this._inflate = null;\n        this.params = null;\n        if (!zlibLimiter) {\n            const concurrency = this._options.concurrencyLimit !== undefined ? this._options.concurrencyLimit : 10;\n            zlibLimiter = new Limiter(concurrency);\n        }\n    }\n    /**\n   * @type {String}\n   */ static get extensionName() {\n        return \"permessage-deflate\";\n    }\n    /**\n   * Create an extension negotiation offer.\n   *\n   * @return {Object} Extension parameters\n   * @public\n   */ offer() {\n        const params = {};\n        if (this._options.serverNoContextTakeover) {\n            params.server_no_context_takeover = true;\n        }\n        if (this._options.clientNoContextTakeover) {\n            params.client_no_context_takeover = true;\n        }\n        if (this._options.serverMaxWindowBits) {\n            params.server_max_window_bits = this._options.serverMaxWindowBits;\n        }\n        if (this._options.clientMaxWindowBits) {\n            params.client_max_window_bits = this._options.clientMaxWindowBits;\n        } else if (this._options.clientMaxWindowBits == null) {\n            params.client_max_window_bits = true;\n        }\n        return params;\n    }\n    /**\n   * Accept an extension negotiation offer/response.\n   *\n   * @param {Array} configurations The extension negotiation offers/reponse\n   * @return {Object} Accepted configuration\n   * @public\n   */ accept(configurations) {\n        configurations = this.normalizeParams(configurations);\n        this.params = this._isServer ? this.acceptAsServer(configurations) : this.acceptAsClient(configurations);\n        return this.params;\n    }\n    /**\n   * Releases all resources used by the extension.\n   *\n   * @public\n   */ cleanup() {\n        if (this._inflate) {\n            this._inflate.close();\n            this._inflate = null;\n        }\n        if (this._deflate) {\n            const callback = this._deflate[kCallback];\n            this._deflate.close();\n            this._deflate = null;\n            if (callback) {\n                callback(new Error(\"The deflate stream was closed while data was being processed\"));\n            }\n        }\n    }\n    /**\n   *  Accept an extension negotiation offer.\n   *\n   * @param {Array} offers The extension negotiation offers\n   * @return {Object} Accepted configuration\n   * @private\n   */ acceptAsServer(offers) {\n        const opts = this._options;\n        const accepted = offers.find((params)=>{\n            if (opts.serverNoContextTakeover === false && params.server_no_context_takeover || params.server_max_window_bits && (opts.serverMaxWindowBits === false || typeof opts.serverMaxWindowBits === \"number\" && opts.serverMaxWindowBits > params.server_max_window_bits) || typeof opts.clientMaxWindowBits === \"number\" && !params.client_max_window_bits) {\n                return false;\n            }\n            return true;\n        });\n        if (!accepted) {\n            throw new Error(\"None of the extension offers can be accepted\");\n        }\n        if (opts.serverNoContextTakeover) {\n            accepted.server_no_context_takeover = true;\n        }\n        if (opts.clientNoContextTakeover) {\n            accepted.client_no_context_takeover = true;\n        }\n        if (typeof opts.serverMaxWindowBits === \"number\") {\n            accepted.server_max_window_bits = opts.serverMaxWindowBits;\n        }\n        if (typeof opts.clientMaxWindowBits === \"number\") {\n            accepted.client_max_window_bits = opts.clientMaxWindowBits;\n        } else if (accepted.client_max_window_bits === true || opts.clientMaxWindowBits === false) {\n            delete accepted.client_max_window_bits;\n        }\n        return accepted;\n    }\n    /**\n   * Accept the extension negotiation response.\n   *\n   * @param {Array} response The extension negotiation response\n   * @return {Object} Accepted configuration\n   * @private\n   */ acceptAsClient(response) {\n        const params = response[0];\n        if (this._options.clientNoContextTakeover === false && params.client_no_context_takeover) {\n            throw new Error('Unexpected parameter \"client_no_context_takeover\"');\n        }\n        if (!params.client_max_window_bits) {\n            if (typeof this._options.clientMaxWindowBits === \"number\") {\n                params.client_max_window_bits = this._options.clientMaxWindowBits;\n            }\n        } else if (this._options.clientMaxWindowBits === false || typeof this._options.clientMaxWindowBits === \"number\" && params.client_max_window_bits > this._options.clientMaxWindowBits) {\n            throw new Error('Unexpected or invalid parameter \"client_max_window_bits\"');\n        }\n        return params;\n    }\n    /**\n   * Normalize parameters.\n   *\n   * @param {Array} configurations The extension negotiation offers/reponse\n   * @return {Array} The offers/response with normalized parameters\n   * @private\n   */ normalizeParams(configurations) {\n        configurations.forEach((params)=>{\n            Object.keys(params).forEach((key)=>{\n                let value = params[key];\n                if (value.length > 1) {\n                    throw new Error(`Parameter \"${key}\" must have only a single value`);\n                }\n                value = value[0];\n                if (key === \"client_max_window_bits\") {\n                    if (value !== true) {\n                        const num = +value;\n                        if (!Number.isInteger(num) || num < 8 || num > 15) {\n                            throw new TypeError(`Invalid value for parameter \"${key}\": ${value}`);\n                        }\n                        value = num;\n                    } else if (!this._isServer) {\n                        throw new TypeError(`Invalid value for parameter \"${key}\": ${value}`);\n                    }\n                } else if (key === \"server_max_window_bits\") {\n                    const num = +value;\n                    if (!Number.isInteger(num) || num < 8 || num > 15) {\n                        throw new TypeError(`Invalid value for parameter \"${key}\": ${value}`);\n                    }\n                    value = num;\n                } else if (key === \"client_no_context_takeover\" || key === \"server_no_context_takeover\") {\n                    if (value !== true) {\n                        throw new TypeError(`Invalid value for parameter \"${key}\": ${value}`);\n                    }\n                } else {\n                    throw new Error(`Unknown parameter \"${key}\"`);\n                }\n                params[key] = value;\n            });\n        });\n        return configurations;\n    }\n    /**\n   * Decompress data. Concurrency limited.\n   *\n   * @param {Buffer} data Compressed data\n   * @param {Boolean} fin Specifies whether or not this is the last fragment\n   * @param {Function} callback Callback\n   * @public\n   */ decompress(data, fin, callback) {\n        zlibLimiter.add((done)=>{\n            this._decompress(data, fin, (err, result)=>{\n                done();\n                callback(err, result);\n            });\n        });\n    }\n    /**\n   * Compress data. Concurrency limited.\n   *\n   * @param {(Buffer|String)} data Data to compress\n   * @param {Boolean} fin Specifies whether or not this is the last fragment\n   * @param {Function} callback Callback\n   * @public\n   */ compress(data, fin, callback) {\n        zlibLimiter.add((done)=>{\n            this._compress(data, fin, (err, result)=>{\n                done();\n                callback(err, result);\n            });\n        });\n    }\n    /**\n   * Decompress data.\n   *\n   * @param {Buffer} data Compressed data\n   * @param {Boolean} fin Specifies whether or not this is the last fragment\n   * @param {Function} callback Callback\n   * @private\n   */ _decompress(data, fin, callback) {\n        const endpoint = this._isServer ? \"client\" : \"server\";\n        if (!this._inflate) {\n            const key = `${endpoint}_max_window_bits`;\n            const windowBits = typeof this.params[key] !== \"number\" ? zlib.Z_DEFAULT_WINDOWBITS : this.params[key];\n            this._inflate = zlib.createInflateRaw({\n                ...this._options.zlibInflateOptions,\n                windowBits\n            });\n            this._inflate[kPerMessageDeflate] = this;\n            this._inflate[kTotalLength] = 0;\n            this._inflate[kBuffers] = [];\n            this._inflate.on(\"error\", inflateOnError);\n            this._inflate.on(\"data\", inflateOnData);\n        }\n        this._inflate[kCallback] = callback;\n        this._inflate.write(data);\n        if (fin) this._inflate.write(TRAILER);\n        this._inflate.flush(()=>{\n            const err = this._inflate[kError];\n            if (err) {\n                this._inflate.close();\n                this._inflate = null;\n                callback(err);\n                return;\n            }\n            const data = bufferUtil.concat(this._inflate[kBuffers], this._inflate[kTotalLength]);\n            if (this._inflate._readableState.endEmitted) {\n                this._inflate.close();\n                this._inflate = null;\n            } else {\n                this._inflate[kTotalLength] = 0;\n                this._inflate[kBuffers] = [];\n                if (fin && this.params[`${endpoint}_no_context_takeover`]) {\n                    this._inflate.reset();\n                }\n            }\n            callback(null, data);\n        });\n    }\n    /**\n   * Compress data.\n   *\n   * @param {(Buffer|String)} data Data to compress\n   * @param {Boolean} fin Specifies whether or not this is the last fragment\n   * @param {Function} callback Callback\n   * @private\n   */ _compress(data, fin, callback) {\n        const endpoint = this._isServer ? \"server\" : \"client\";\n        if (!this._deflate) {\n            const key = `${endpoint}_max_window_bits`;\n            const windowBits = typeof this.params[key] !== \"number\" ? zlib.Z_DEFAULT_WINDOWBITS : this.params[key];\n            this._deflate = zlib.createDeflateRaw({\n                ...this._options.zlibDeflateOptions,\n                windowBits\n            });\n            this._deflate[kTotalLength] = 0;\n            this._deflate[kBuffers] = [];\n            this._deflate.on(\"data\", deflateOnData);\n        }\n        this._deflate[kCallback] = callback;\n        this._deflate.write(data);\n        this._deflate.flush(zlib.Z_SYNC_FLUSH, ()=>{\n            if (!this._deflate) {\n                //\n                // The deflate stream was closed while data was being processed.\n                //\n                return;\n            }\n            let data = bufferUtil.concat(this._deflate[kBuffers], this._deflate[kTotalLength]);\n            if (fin) {\n                data = new FastBuffer(data.buffer, data.byteOffset, data.length - 4);\n            }\n            //\n            // Ensure that the callback will not be called again in\n            // `PerMessageDeflate#cleanup()`.\n            //\n            this._deflate[kCallback] = null;\n            this._deflate[kTotalLength] = 0;\n            this._deflate[kBuffers] = [];\n            if (fin && this.params[`${endpoint}_no_context_takeover`]) {\n                this._deflate.reset();\n            }\n            callback(null, data);\n        });\n    }\n}\nmodule.exports = PerMessageDeflate;\n/**\n * The listener of the `zlib.DeflateRaw` stream `'data'` event.\n *\n * @param {Buffer} chunk A chunk of data\n * @private\n */ function deflateOnData(chunk) {\n    this[kBuffers].push(chunk);\n    this[kTotalLength] += chunk.length;\n}\n/**\n * The listener of the `zlib.InflateRaw` stream `'data'` event.\n *\n * @param {Buffer} chunk A chunk of data\n * @private\n */ function inflateOnData(chunk) {\n    this[kTotalLength] += chunk.length;\n    if (this[kPerMessageDeflate]._maxPayload < 1 || this[kTotalLength] <= this[kPerMessageDeflate]._maxPayload) {\n        this[kBuffers].push(chunk);\n        return;\n    }\n    this[kError] = new RangeError(\"Max payload size exceeded\");\n    this[kError].code = \"WS_ERR_UNSUPPORTED_MESSAGE_LENGTH\";\n    this[kError][kStatusCode] = 1009;\n    this.removeListener(\"data\", inflateOnData);\n    //\n    // The choice to employ `zlib.reset()` over `zlib.close()` is dictated by the\n    // fact that in Node.js versions prior to 13.10.0, the callback for\n    // `zlib.flush()` is not called if `zlib.close()` is used. Utilizing\n    // `zlib.reset()` ensures that either the callback is invoked or an error is\n    // emitted.\n    //\n    this.reset();\n}\n/**\n * The listener of the `zlib.InflateRaw` stream `'error'` event.\n *\n * @param {Error} err The emitted error\n * @private\n */ function inflateOnError(err) {\n    //\n    // There is no need to call `Zlib#close()` as the handle is automatically\n    // closed when an error is emitted.\n    //\n    this[kPerMessageDeflate]._inflate = null;\n    if (this[kError]) {\n        this[kCallback](this[kError]);\n        return;\n    }\n    err[kStatusCode] = 1007;\n    this[kCallback](err);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ws/lib/permessage-deflate.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ws/lib/receiver.js":
/*!*****************************************!*\
  !*** ./node_modules/ws/lib/receiver.js ***!
  \*****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst { Writable } = __webpack_require__(/*! stream */ \"stream\");\nconst PerMessageDeflate = __webpack_require__(/*! ./permessage-deflate */ \"(ssr)/./node_modules/ws/lib/permessage-deflate.js\");\nconst { BINARY_TYPES, EMPTY_BUFFER, kStatusCode, kWebSocket } = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/ws/lib/constants.js\");\nconst { concat, toArrayBuffer, unmask } = __webpack_require__(/*! ./buffer-util */ \"(ssr)/./node_modules/ws/lib/buffer-util.js\");\nconst { isValidStatusCode, isValidUTF8 } = __webpack_require__(/*! ./validation */ \"(ssr)/./node_modules/ws/lib/validation.js\");\nconst FastBuffer = Buffer[Symbol.species];\nconst GET_INFO = 0;\nconst GET_PAYLOAD_LENGTH_16 = 1;\nconst GET_PAYLOAD_LENGTH_64 = 2;\nconst GET_MASK = 3;\nconst GET_DATA = 4;\nconst INFLATING = 5;\nconst DEFER_EVENT = 6;\n/**\n * HyBi Receiver implementation.\n *\n * @extends Writable\n */ class Receiver extends Writable {\n    /**\n   * Creates a Receiver instance.\n   *\n   * @param {Object} [options] Options object\n   * @param {Boolean} [options.allowSynchronousEvents=true] Specifies whether\n   *     any of the `'message'`, `'ping'`, and `'pong'` events can be emitted\n   *     multiple times in the same tick\n   * @param {String} [options.binaryType=nodebuffer] The type for binary data\n   * @param {Object} [options.extensions] An object containing the negotiated\n   *     extensions\n   * @param {Boolean} [options.isServer=false] Specifies whether to operate in\n   *     client or server mode\n   * @param {Number} [options.maxPayload=0] The maximum allowed message length\n   * @param {Boolean} [options.skipUTF8Validation=false] Specifies whether or\n   *     not to skip UTF-8 validation for text and close messages\n   */ constructor(options = {}){\n        super();\n        this._allowSynchronousEvents = options.allowSynchronousEvents !== undefined ? options.allowSynchronousEvents : true;\n        this._binaryType = options.binaryType || BINARY_TYPES[0];\n        this._extensions = options.extensions || {};\n        this._isServer = !!options.isServer;\n        this._maxPayload = options.maxPayload | 0;\n        this._skipUTF8Validation = !!options.skipUTF8Validation;\n        this[kWebSocket] = undefined;\n        this._bufferedBytes = 0;\n        this._buffers = [];\n        this._compressed = false;\n        this._payloadLength = 0;\n        this._mask = undefined;\n        this._fragmented = 0;\n        this._masked = false;\n        this._fin = false;\n        this._opcode = 0;\n        this._totalPayloadLength = 0;\n        this._messageLength = 0;\n        this._fragments = [];\n        this._errored = false;\n        this._loop = false;\n        this._state = GET_INFO;\n    }\n    /**\n   * Implements `Writable.prototype._write()`.\n   *\n   * @param {Buffer} chunk The chunk of data to write\n   * @param {String} encoding The character encoding of `chunk`\n   * @param {Function} cb Callback\n   * @private\n   */ _write(chunk, encoding, cb) {\n        if (this._opcode === 0x08 && this._state == GET_INFO) return cb();\n        this._bufferedBytes += chunk.length;\n        this._buffers.push(chunk);\n        this.startLoop(cb);\n    }\n    /**\n   * Consumes `n` bytes from the buffered data.\n   *\n   * @param {Number} n The number of bytes to consume\n   * @return {Buffer} The consumed bytes\n   * @private\n   */ consume(n) {\n        this._bufferedBytes -= n;\n        if (n === this._buffers[0].length) return this._buffers.shift();\n        if (n < this._buffers[0].length) {\n            const buf = this._buffers[0];\n            this._buffers[0] = new FastBuffer(buf.buffer, buf.byteOffset + n, buf.length - n);\n            return new FastBuffer(buf.buffer, buf.byteOffset, n);\n        }\n        const dst = Buffer.allocUnsafe(n);\n        do {\n            const buf = this._buffers[0];\n            const offset = dst.length - n;\n            if (n >= buf.length) {\n                dst.set(this._buffers.shift(), offset);\n            } else {\n                dst.set(new Uint8Array(buf.buffer, buf.byteOffset, n), offset);\n                this._buffers[0] = new FastBuffer(buf.buffer, buf.byteOffset + n, buf.length - n);\n            }\n            n -= buf.length;\n        }while (n > 0);\n        return dst;\n    }\n    /**\n   * Starts the parsing loop.\n   *\n   * @param {Function} cb Callback\n   * @private\n   */ startLoop(cb) {\n        this._loop = true;\n        do {\n            switch(this._state){\n                case GET_INFO:\n                    this.getInfo(cb);\n                    break;\n                case GET_PAYLOAD_LENGTH_16:\n                    this.getPayloadLength16(cb);\n                    break;\n                case GET_PAYLOAD_LENGTH_64:\n                    this.getPayloadLength64(cb);\n                    break;\n                case GET_MASK:\n                    this.getMask();\n                    break;\n                case GET_DATA:\n                    this.getData(cb);\n                    break;\n                case INFLATING:\n                case DEFER_EVENT:\n                    this._loop = false;\n                    return;\n            }\n        }while (this._loop);\n        if (!this._errored) cb();\n    }\n    /**\n   * Reads the first two bytes of a frame.\n   *\n   * @param {Function} cb Callback\n   * @private\n   */ getInfo(cb) {\n        if (this._bufferedBytes < 2) {\n            this._loop = false;\n            return;\n        }\n        const buf = this.consume(2);\n        if ((buf[0] & 0x30) !== 0x00) {\n            const error = this.createError(RangeError, \"RSV2 and RSV3 must be clear\", true, 1002, \"WS_ERR_UNEXPECTED_RSV_2_3\");\n            cb(error);\n            return;\n        }\n        const compressed = (buf[0] & 0x40) === 0x40;\n        if (compressed && !this._extensions[PerMessageDeflate.extensionName]) {\n            const error = this.createError(RangeError, \"RSV1 must be clear\", true, 1002, \"WS_ERR_UNEXPECTED_RSV_1\");\n            cb(error);\n            return;\n        }\n        this._fin = (buf[0] & 0x80) === 0x80;\n        this._opcode = buf[0] & 0x0f;\n        this._payloadLength = buf[1] & 0x7f;\n        if (this._opcode === 0x00) {\n            if (compressed) {\n                const error = this.createError(RangeError, \"RSV1 must be clear\", true, 1002, \"WS_ERR_UNEXPECTED_RSV_1\");\n                cb(error);\n                return;\n            }\n            if (!this._fragmented) {\n                const error = this.createError(RangeError, \"invalid opcode 0\", true, 1002, \"WS_ERR_INVALID_OPCODE\");\n                cb(error);\n                return;\n            }\n            this._opcode = this._fragmented;\n        } else if (this._opcode === 0x01 || this._opcode === 0x02) {\n            if (this._fragmented) {\n                const error = this.createError(RangeError, `invalid opcode ${this._opcode}`, true, 1002, \"WS_ERR_INVALID_OPCODE\");\n                cb(error);\n                return;\n            }\n            this._compressed = compressed;\n        } else if (this._opcode > 0x07 && this._opcode < 0x0b) {\n            if (!this._fin) {\n                const error = this.createError(RangeError, \"FIN must be set\", true, 1002, \"WS_ERR_EXPECTED_FIN\");\n                cb(error);\n                return;\n            }\n            if (compressed) {\n                const error = this.createError(RangeError, \"RSV1 must be clear\", true, 1002, \"WS_ERR_UNEXPECTED_RSV_1\");\n                cb(error);\n                return;\n            }\n            if (this._payloadLength > 0x7d || this._opcode === 0x08 && this._payloadLength === 1) {\n                const error = this.createError(RangeError, `invalid payload length ${this._payloadLength}`, true, 1002, \"WS_ERR_INVALID_CONTROL_PAYLOAD_LENGTH\");\n                cb(error);\n                return;\n            }\n        } else {\n            const error = this.createError(RangeError, `invalid opcode ${this._opcode}`, true, 1002, \"WS_ERR_INVALID_OPCODE\");\n            cb(error);\n            return;\n        }\n        if (!this._fin && !this._fragmented) this._fragmented = this._opcode;\n        this._masked = (buf[1] & 0x80) === 0x80;\n        if (this._isServer) {\n            if (!this._masked) {\n                const error = this.createError(RangeError, \"MASK must be set\", true, 1002, \"WS_ERR_EXPECTED_MASK\");\n                cb(error);\n                return;\n            }\n        } else if (this._masked) {\n            const error = this.createError(RangeError, \"MASK must be clear\", true, 1002, \"WS_ERR_UNEXPECTED_MASK\");\n            cb(error);\n            return;\n        }\n        if (this._payloadLength === 126) this._state = GET_PAYLOAD_LENGTH_16;\n        else if (this._payloadLength === 127) this._state = GET_PAYLOAD_LENGTH_64;\n        else this.haveLength(cb);\n    }\n    /**\n   * Gets extended payload length (7+16).\n   *\n   * @param {Function} cb Callback\n   * @private\n   */ getPayloadLength16(cb) {\n        if (this._bufferedBytes < 2) {\n            this._loop = false;\n            return;\n        }\n        this._payloadLength = this.consume(2).readUInt16BE(0);\n        this.haveLength(cb);\n    }\n    /**\n   * Gets extended payload length (7+64).\n   *\n   * @param {Function} cb Callback\n   * @private\n   */ getPayloadLength64(cb) {\n        if (this._bufferedBytes < 8) {\n            this._loop = false;\n            return;\n        }\n        const buf = this.consume(8);\n        const num = buf.readUInt32BE(0);\n        //\n        // The maximum safe integer in JavaScript is 2^53 - 1. An error is returned\n        // if payload length is greater than this number.\n        //\n        if (num > Math.pow(2, 53 - 32) - 1) {\n            const error = this.createError(RangeError, \"Unsupported WebSocket frame: payload length > 2^53 - 1\", false, 1009, \"WS_ERR_UNSUPPORTED_DATA_PAYLOAD_LENGTH\");\n            cb(error);\n            return;\n        }\n        this._payloadLength = num * Math.pow(2, 32) + buf.readUInt32BE(4);\n        this.haveLength(cb);\n    }\n    /**\n   * Payload length has been read.\n   *\n   * @param {Function} cb Callback\n   * @private\n   */ haveLength(cb) {\n        if (this._payloadLength && this._opcode < 0x08) {\n            this._totalPayloadLength += this._payloadLength;\n            if (this._totalPayloadLength > this._maxPayload && this._maxPayload > 0) {\n                const error = this.createError(RangeError, \"Max payload size exceeded\", false, 1009, \"WS_ERR_UNSUPPORTED_MESSAGE_LENGTH\");\n                cb(error);\n                return;\n            }\n        }\n        if (this._masked) this._state = GET_MASK;\n        else this._state = GET_DATA;\n    }\n    /**\n   * Reads mask bytes.\n   *\n   * @private\n   */ getMask() {\n        if (this._bufferedBytes < 4) {\n            this._loop = false;\n            return;\n        }\n        this._mask = this.consume(4);\n        this._state = GET_DATA;\n    }\n    /**\n   * Reads data bytes.\n   *\n   * @param {Function} cb Callback\n   * @private\n   */ getData(cb) {\n        let data = EMPTY_BUFFER;\n        if (this._payloadLength) {\n            if (this._bufferedBytes < this._payloadLength) {\n                this._loop = false;\n                return;\n            }\n            data = this.consume(this._payloadLength);\n            if (this._masked && (this._mask[0] | this._mask[1] | this._mask[2] | this._mask[3]) !== 0) {\n                unmask(data, this._mask);\n            }\n        }\n        if (this._opcode > 0x07) {\n            this.controlMessage(data, cb);\n            return;\n        }\n        if (this._compressed) {\n            this._state = INFLATING;\n            this.decompress(data, cb);\n            return;\n        }\n        if (data.length) {\n            //\n            // This message is not compressed so its length is the sum of the payload\n            // length of all fragments.\n            //\n            this._messageLength = this._totalPayloadLength;\n            this._fragments.push(data);\n        }\n        this.dataMessage(cb);\n    }\n    /**\n   * Decompresses data.\n   *\n   * @param {Buffer} data Compressed data\n   * @param {Function} cb Callback\n   * @private\n   */ decompress(data, cb) {\n        const perMessageDeflate = this._extensions[PerMessageDeflate.extensionName];\n        perMessageDeflate.decompress(data, this._fin, (err, buf)=>{\n            if (err) return cb(err);\n            if (buf.length) {\n                this._messageLength += buf.length;\n                if (this._messageLength > this._maxPayload && this._maxPayload > 0) {\n                    const error = this.createError(RangeError, \"Max payload size exceeded\", false, 1009, \"WS_ERR_UNSUPPORTED_MESSAGE_LENGTH\");\n                    cb(error);\n                    return;\n                }\n                this._fragments.push(buf);\n            }\n            this.dataMessage(cb);\n            if (this._state === GET_INFO) this.startLoop(cb);\n        });\n    }\n    /**\n   * Handles a data message.\n   *\n   * @param {Function} cb Callback\n   * @private\n   */ dataMessage(cb) {\n        if (!this._fin) {\n            this._state = GET_INFO;\n            return;\n        }\n        const messageLength = this._messageLength;\n        const fragments = this._fragments;\n        this._totalPayloadLength = 0;\n        this._messageLength = 0;\n        this._fragmented = 0;\n        this._fragments = [];\n        if (this._opcode === 2) {\n            let data;\n            if (this._binaryType === \"nodebuffer\") {\n                data = concat(fragments, messageLength);\n            } else if (this._binaryType === \"arraybuffer\") {\n                data = toArrayBuffer(concat(fragments, messageLength));\n            } else if (this._binaryType === \"blob\") {\n                data = new Blob(fragments);\n            } else {\n                data = fragments;\n            }\n            if (this._allowSynchronousEvents) {\n                this.emit(\"message\", data, true);\n                this._state = GET_INFO;\n            } else {\n                this._state = DEFER_EVENT;\n                setImmediate(()=>{\n                    this.emit(\"message\", data, true);\n                    this._state = GET_INFO;\n                    this.startLoop(cb);\n                });\n            }\n        } else {\n            const buf = concat(fragments, messageLength);\n            if (!this._skipUTF8Validation && !isValidUTF8(buf)) {\n                const error = this.createError(Error, \"invalid UTF-8 sequence\", true, 1007, \"WS_ERR_INVALID_UTF8\");\n                cb(error);\n                return;\n            }\n            if (this._state === INFLATING || this._allowSynchronousEvents) {\n                this.emit(\"message\", buf, false);\n                this._state = GET_INFO;\n            } else {\n                this._state = DEFER_EVENT;\n                setImmediate(()=>{\n                    this.emit(\"message\", buf, false);\n                    this._state = GET_INFO;\n                    this.startLoop(cb);\n                });\n            }\n        }\n    }\n    /**\n   * Handles a control message.\n   *\n   * @param {Buffer} data Data to handle\n   * @return {(Error|RangeError|undefined)} A possible error\n   * @private\n   */ controlMessage(data, cb) {\n        if (this._opcode === 0x08) {\n            if (data.length === 0) {\n                this._loop = false;\n                this.emit(\"conclude\", 1005, EMPTY_BUFFER);\n                this.end();\n            } else {\n                const code = data.readUInt16BE(0);\n                if (!isValidStatusCode(code)) {\n                    const error = this.createError(RangeError, `invalid status code ${code}`, true, 1002, \"WS_ERR_INVALID_CLOSE_CODE\");\n                    cb(error);\n                    return;\n                }\n                const buf = new FastBuffer(data.buffer, data.byteOffset + 2, data.length - 2);\n                if (!this._skipUTF8Validation && !isValidUTF8(buf)) {\n                    const error = this.createError(Error, \"invalid UTF-8 sequence\", true, 1007, \"WS_ERR_INVALID_UTF8\");\n                    cb(error);\n                    return;\n                }\n                this._loop = false;\n                this.emit(\"conclude\", code, buf);\n                this.end();\n            }\n            this._state = GET_INFO;\n            return;\n        }\n        if (this._allowSynchronousEvents) {\n            this.emit(this._opcode === 0x09 ? \"ping\" : \"pong\", data);\n            this._state = GET_INFO;\n        } else {\n            this._state = DEFER_EVENT;\n            setImmediate(()=>{\n                this.emit(this._opcode === 0x09 ? \"ping\" : \"pong\", data);\n                this._state = GET_INFO;\n                this.startLoop(cb);\n            });\n        }\n    }\n    /**\n   * Builds an error object.\n   *\n   * @param {function(new:Error|RangeError)} ErrorCtor The error constructor\n   * @param {String} message The error message\n   * @param {Boolean} prefix Specifies whether or not to add a default prefix to\n   *     `message`\n   * @param {Number} statusCode The status code\n   * @param {String} errorCode The exposed error code\n   * @return {(Error|RangeError)} The error\n   * @private\n   */ createError(ErrorCtor, message, prefix, statusCode, errorCode) {\n        this._loop = false;\n        this._errored = true;\n        const err = new ErrorCtor(prefix ? `Invalid WebSocket frame: ${message}` : message);\n        Error.captureStackTrace(err, this.createError);\n        err.code = errorCode;\n        err[kStatusCode] = statusCode;\n        return err;\n    }\n}\nmodule.exports = Receiver;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ws/lib/receiver.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ws/lib/sender.js":
/*!***************************************!*\
  !*** ./node_modules/ws/lib/sender.js ***!
  \***************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/* eslint no-unused-vars: [\"error\", { \"varsIgnorePattern\": \"^Duplex\" }] */ \nconst { Duplex } = __webpack_require__(/*! stream */ \"stream\");\nconst { randomFillSync } = __webpack_require__(/*! crypto */ \"crypto\");\nconst PerMessageDeflate = __webpack_require__(/*! ./permessage-deflate */ \"(ssr)/./node_modules/ws/lib/permessage-deflate.js\");\nconst { EMPTY_BUFFER, kWebSocket, NOOP } = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/ws/lib/constants.js\");\nconst { isBlob, isValidStatusCode } = __webpack_require__(/*! ./validation */ \"(ssr)/./node_modules/ws/lib/validation.js\");\nconst { mask: applyMask, toBuffer } = __webpack_require__(/*! ./buffer-util */ \"(ssr)/./node_modules/ws/lib/buffer-util.js\");\nconst kByteLength = Symbol(\"kByteLength\");\nconst maskBuffer = Buffer.alloc(4);\nconst RANDOM_POOL_SIZE = 8 * 1024;\nlet randomPool;\nlet randomPoolPointer = RANDOM_POOL_SIZE;\nconst DEFAULT = 0;\nconst DEFLATING = 1;\nconst GET_BLOB_DATA = 2;\n/**\n * HyBi Sender implementation.\n */ class Sender {\n    /**\n   * Creates a Sender instance.\n   *\n   * @param {Duplex} socket The connection socket\n   * @param {Object} [extensions] An object containing the negotiated extensions\n   * @param {Function} [generateMask] The function used to generate the masking\n   *     key\n   */ constructor(socket, extensions, generateMask){\n        this._extensions = extensions || {};\n        if (generateMask) {\n            this._generateMask = generateMask;\n            this._maskBuffer = Buffer.alloc(4);\n        }\n        this._socket = socket;\n        this._firstFragment = true;\n        this._compress = false;\n        this._bufferedBytes = 0;\n        this._queue = [];\n        this._state = DEFAULT;\n        this.onerror = NOOP;\n        this[kWebSocket] = undefined;\n    }\n    /**\n   * Frames a piece of data according to the HyBi WebSocket protocol.\n   *\n   * @param {(Buffer|String)} data The data to frame\n   * @param {Object} options Options object\n   * @param {Boolean} [options.fin=false] Specifies whether or not to set the\n   *     FIN bit\n   * @param {Function} [options.generateMask] The function used to generate the\n   *     masking key\n   * @param {Boolean} [options.mask=false] Specifies whether or not to mask\n   *     `data`\n   * @param {Buffer} [options.maskBuffer] The buffer used to store the masking\n   *     key\n   * @param {Number} options.opcode The opcode\n   * @param {Boolean} [options.readOnly=false] Specifies whether `data` can be\n   *     modified\n   * @param {Boolean} [options.rsv1=false] Specifies whether or not to set the\n   *     RSV1 bit\n   * @return {(Buffer|String)[]} The framed data\n   * @public\n   */ static frame(data, options) {\n        let mask;\n        let merge = false;\n        let offset = 2;\n        let skipMasking = false;\n        if (options.mask) {\n            mask = options.maskBuffer || maskBuffer;\n            if (options.generateMask) {\n                options.generateMask(mask);\n            } else {\n                if (randomPoolPointer === RANDOM_POOL_SIZE) {\n                    /* istanbul ignore else  */ if (randomPool === undefined) {\n                        //\n                        // This is lazily initialized because server-sent frames must not\n                        // be masked so it may never be used.\n                        //\n                        randomPool = Buffer.alloc(RANDOM_POOL_SIZE);\n                    }\n                    randomFillSync(randomPool, 0, RANDOM_POOL_SIZE);\n                    randomPoolPointer = 0;\n                }\n                mask[0] = randomPool[randomPoolPointer++];\n                mask[1] = randomPool[randomPoolPointer++];\n                mask[2] = randomPool[randomPoolPointer++];\n                mask[3] = randomPool[randomPoolPointer++];\n            }\n            skipMasking = (mask[0] | mask[1] | mask[2] | mask[3]) === 0;\n            offset = 6;\n        }\n        let dataLength;\n        if (typeof data === \"string\") {\n            if ((!options.mask || skipMasking) && options[kByteLength] !== undefined) {\n                dataLength = options[kByteLength];\n            } else {\n                data = Buffer.from(data);\n                dataLength = data.length;\n            }\n        } else {\n            dataLength = data.length;\n            merge = options.mask && options.readOnly && !skipMasking;\n        }\n        let payloadLength = dataLength;\n        if (dataLength >= 65536) {\n            offset += 8;\n            payloadLength = 127;\n        } else if (dataLength > 125) {\n            offset += 2;\n            payloadLength = 126;\n        }\n        const target = Buffer.allocUnsafe(merge ? dataLength + offset : offset);\n        target[0] = options.fin ? options.opcode | 0x80 : options.opcode;\n        if (options.rsv1) target[0] |= 0x40;\n        target[1] = payloadLength;\n        if (payloadLength === 126) {\n            target.writeUInt16BE(dataLength, 2);\n        } else if (payloadLength === 127) {\n            target[2] = target[3] = 0;\n            target.writeUIntBE(dataLength, 4, 6);\n        }\n        if (!options.mask) return [\n            target,\n            data\n        ];\n        target[1] |= 0x80;\n        target[offset - 4] = mask[0];\n        target[offset - 3] = mask[1];\n        target[offset - 2] = mask[2];\n        target[offset - 1] = mask[3];\n        if (skipMasking) return [\n            target,\n            data\n        ];\n        if (merge) {\n            applyMask(data, mask, target, offset, dataLength);\n            return [\n                target\n            ];\n        }\n        applyMask(data, mask, data, 0, dataLength);\n        return [\n            target,\n            data\n        ];\n    }\n    /**\n   * Sends a close message to the other peer.\n   *\n   * @param {Number} [code] The status code component of the body\n   * @param {(String|Buffer)} [data] The message component of the body\n   * @param {Boolean} [mask=false] Specifies whether or not to mask the message\n   * @param {Function} [cb] Callback\n   * @public\n   */ close(code, data, mask, cb) {\n        let buf;\n        if (code === undefined) {\n            buf = EMPTY_BUFFER;\n        } else if (typeof code !== \"number\" || !isValidStatusCode(code)) {\n            throw new TypeError(\"First argument must be a valid error code number\");\n        } else if (data === undefined || !data.length) {\n            buf = Buffer.allocUnsafe(2);\n            buf.writeUInt16BE(code, 0);\n        } else {\n            const length = Buffer.byteLength(data);\n            if (length > 123) {\n                throw new RangeError(\"The message must not be greater than 123 bytes\");\n            }\n            buf = Buffer.allocUnsafe(2 + length);\n            buf.writeUInt16BE(code, 0);\n            if (typeof data === \"string\") {\n                buf.write(data, 2);\n            } else {\n                buf.set(data, 2);\n            }\n        }\n        const options = {\n            [kByteLength]: buf.length,\n            fin: true,\n            generateMask: this._generateMask,\n            mask,\n            maskBuffer: this._maskBuffer,\n            opcode: 0x08,\n            readOnly: false,\n            rsv1: false\n        };\n        if (this._state !== DEFAULT) {\n            this.enqueue([\n                this.dispatch,\n                buf,\n                false,\n                options,\n                cb\n            ]);\n        } else {\n            this.sendFrame(Sender.frame(buf, options), cb);\n        }\n    }\n    /**\n   * Sends a ping message to the other peer.\n   *\n   * @param {*} data The message to send\n   * @param {Boolean} [mask=false] Specifies whether or not to mask `data`\n   * @param {Function} [cb] Callback\n   * @public\n   */ ping(data, mask, cb) {\n        let byteLength;\n        let readOnly;\n        if (typeof data === \"string\") {\n            byteLength = Buffer.byteLength(data);\n            readOnly = false;\n        } else if (isBlob(data)) {\n            byteLength = data.size;\n            readOnly = false;\n        } else {\n            data = toBuffer(data);\n            byteLength = data.length;\n            readOnly = toBuffer.readOnly;\n        }\n        if (byteLength > 125) {\n            throw new RangeError(\"The data size must not be greater than 125 bytes\");\n        }\n        const options = {\n            [kByteLength]: byteLength,\n            fin: true,\n            generateMask: this._generateMask,\n            mask,\n            maskBuffer: this._maskBuffer,\n            opcode: 0x09,\n            readOnly,\n            rsv1: false\n        };\n        if (isBlob(data)) {\n            if (this._state !== DEFAULT) {\n                this.enqueue([\n                    this.getBlobData,\n                    data,\n                    false,\n                    options,\n                    cb\n                ]);\n            } else {\n                this.getBlobData(data, false, options, cb);\n            }\n        } else if (this._state !== DEFAULT) {\n            this.enqueue([\n                this.dispatch,\n                data,\n                false,\n                options,\n                cb\n            ]);\n        } else {\n            this.sendFrame(Sender.frame(data, options), cb);\n        }\n    }\n    /**\n   * Sends a pong message to the other peer.\n   *\n   * @param {*} data The message to send\n   * @param {Boolean} [mask=false] Specifies whether or not to mask `data`\n   * @param {Function} [cb] Callback\n   * @public\n   */ pong(data, mask, cb) {\n        let byteLength;\n        let readOnly;\n        if (typeof data === \"string\") {\n            byteLength = Buffer.byteLength(data);\n            readOnly = false;\n        } else if (isBlob(data)) {\n            byteLength = data.size;\n            readOnly = false;\n        } else {\n            data = toBuffer(data);\n            byteLength = data.length;\n            readOnly = toBuffer.readOnly;\n        }\n        if (byteLength > 125) {\n            throw new RangeError(\"The data size must not be greater than 125 bytes\");\n        }\n        const options = {\n            [kByteLength]: byteLength,\n            fin: true,\n            generateMask: this._generateMask,\n            mask,\n            maskBuffer: this._maskBuffer,\n            opcode: 0x0a,\n            readOnly,\n            rsv1: false\n        };\n        if (isBlob(data)) {\n            if (this._state !== DEFAULT) {\n                this.enqueue([\n                    this.getBlobData,\n                    data,\n                    false,\n                    options,\n                    cb\n                ]);\n            } else {\n                this.getBlobData(data, false, options, cb);\n            }\n        } else if (this._state !== DEFAULT) {\n            this.enqueue([\n                this.dispatch,\n                data,\n                false,\n                options,\n                cb\n            ]);\n        } else {\n            this.sendFrame(Sender.frame(data, options), cb);\n        }\n    }\n    /**\n   * Sends a data message to the other peer.\n   *\n   * @param {*} data The message to send\n   * @param {Object} options Options object\n   * @param {Boolean} [options.binary=false] Specifies whether `data` is binary\n   *     or text\n   * @param {Boolean} [options.compress=false] Specifies whether or not to\n   *     compress `data`\n   * @param {Boolean} [options.fin=false] Specifies whether the fragment is the\n   *     last one\n   * @param {Boolean} [options.mask=false] Specifies whether or not to mask\n   *     `data`\n   * @param {Function} [cb] Callback\n   * @public\n   */ send(data, options, cb) {\n        const perMessageDeflate = this._extensions[PerMessageDeflate.extensionName];\n        let opcode = options.binary ? 2 : 1;\n        let rsv1 = options.compress;\n        let byteLength;\n        let readOnly;\n        if (typeof data === \"string\") {\n            byteLength = Buffer.byteLength(data);\n            readOnly = false;\n        } else if (isBlob(data)) {\n            byteLength = data.size;\n            readOnly = false;\n        } else {\n            data = toBuffer(data);\n            byteLength = data.length;\n            readOnly = toBuffer.readOnly;\n        }\n        if (this._firstFragment) {\n            this._firstFragment = false;\n            if (rsv1 && perMessageDeflate && perMessageDeflate.params[perMessageDeflate._isServer ? \"server_no_context_takeover\" : \"client_no_context_takeover\"]) {\n                rsv1 = byteLength >= perMessageDeflate._threshold;\n            }\n            this._compress = rsv1;\n        } else {\n            rsv1 = false;\n            opcode = 0;\n        }\n        if (options.fin) this._firstFragment = true;\n        const opts = {\n            [kByteLength]: byteLength,\n            fin: options.fin,\n            generateMask: this._generateMask,\n            mask: options.mask,\n            maskBuffer: this._maskBuffer,\n            opcode,\n            readOnly,\n            rsv1\n        };\n        if (isBlob(data)) {\n            if (this._state !== DEFAULT) {\n                this.enqueue([\n                    this.getBlobData,\n                    data,\n                    this._compress,\n                    opts,\n                    cb\n                ]);\n            } else {\n                this.getBlobData(data, this._compress, opts, cb);\n            }\n        } else if (this._state !== DEFAULT) {\n            this.enqueue([\n                this.dispatch,\n                data,\n                this._compress,\n                opts,\n                cb\n            ]);\n        } else {\n            this.dispatch(data, this._compress, opts, cb);\n        }\n    }\n    /**\n   * Gets the contents of a blob as binary data.\n   *\n   * @param {Blob} blob The blob\n   * @param {Boolean} [compress=false] Specifies whether or not to compress\n   *     the data\n   * @param {Object} options Options object\n   * @param {Boolean} [options.fin=false] Specifies whether or not to set the\n   *     FIN bit\n   * @param {Function} [options.generateMask] The function used to generate the\n   *     masking key\n   * @param {Boolean} [options.mask=false] Specifies whether or not to mask\n   *     `data`\n   * @param {Buffer} [options.maskBuffer] The buffer used to store the masking\n   *     key\n   * @param {Number} options.opcode The opcode\n   * @param {Boolean} [options.readOnly=false] Specifies whether `data` can be\n   *     modified\n   * @param {Boolean} [options.rsv1=false] Specifies whether or not to set the\n   *     RSV1 bit\n   * @param {Function} [cb] Callback\n   * @private\n   */ getBlobData(blob, compress, options, cb) {\n        this._bufferedBytes += options[kByteLength];\n        this._state = GET_BLOB_DATA;\n        blob.arrayBuffer().then((arrayBuffer)=>{\n            if (this._socket.destroyed) {\n                const err = new Error(\"The socket was closed while the blob was being read\");\n                //\n                // `callCallbacks` is called in the next tick to ensure that errors\n                // that might be thrown in the callbacks behave like errors thrown\n                // outside the promise chain.\n                //\n                process.nextTick(callCallbacks, this, err, cb);\n                return;\n            }\n            this._bufferedBytes -= options[kByteLength];\n            const data = toBuffer(arrayBuffer);\n            if (!compress) {\n                this._state = DEFAULT;\n                this.sendFrame(Sender.frame(data, options), cb);\n                this.dequeue();\n            } else {\n                this.dispatch(data, compress, options, cb);\n            }\n        }).catch((err)=>{\n            //\n            // `onError` is called in the next tick for the same reason that\n            // `callCallbacks` above is.\n            //\n            process.nextTick(onError, this, err, cb);\n        });\n    }\n    /**\n   * Dispatches a message.\n   *\n   * @param {(Buffer|String)} data The message to send\n   * @param {Boolean} [compress=false] Specifies whether or not to compress\n   *     `data`\n   * @param {Object} options Options object\n   * @param {Boolean} [options.fin=false] Specifies whether or not to set the\n   *     FIN bit\n   * @param {Function} [options.generateMask] The function used to generate the\n   *     masking key\n   * @param {Boolean} [options.mask=false] Specifies whether or not to mask\n   *     `data`\n   * @param {Buffer} [options.maskBuffer] The buffer used to store the masking\n   *     key\n   * @param {Number} options.opcode The opcode\n   * @param {Boolean} [options.readOnly=false] Specifies whether `data` can be\n   *     modified\n   * @param {Boolean} [options.rsv1=false] Specifies whether or not to set the\n   *     RSV1 bit\n   * @param {Function} [cb] Callback\n   * @private\n   */ dispatch(data, compress, options, cb) {\n        if (!compress) {\n            this.sendFrame(Sender.frame(data, options), cb);\n            return;\n        }\n        const perMessageDeflate = this._extensions[PerMessageDeflate.extensionName];\n        this._bufferedBytes += options[kByteLength];\n        this._state = DEFLATING;\n        perMessageDeflate.compress(data, options.fin, (_, buf)=>{\n            if (this._socket.destroyed) {\n                const err = new Error(\"The socket was closed while data was being compressed\");\n                callCallbacks(this, err, cb);\n                return;\n            }\n            this._bufferedBytes -= options[kByteLength];\n            this._state = DEFAULT;\n            options.readOnly = false;\n            this.sendFrame(Sender.frame(buf, options), cb);\n            this.dequeue();\n        });\n    }\n    /**\n   * Executes queued send operations.\n   *\n   * @private\n   */ dequeue() {\n        while(this._state === DEFAULT && this._queue.length){\n            const params = this._queue.shift();\n            this._bufferedBytes -= params[3][kByteLength];\n            Reflect.apply(params[0], this, params.slice(1));\n        }\n    }\n    /**\n   * Enqueues a send operation.\n   *\n   * @param {Array} params Send operation parameters.\n   * @private\n   */ enqueue(params) {\n        this._bufferedBytes += params[3][kByteLength];\n        this._queue.push(params);\n    }\n    /**\n   * Sends a frame.\n   *\n   * @param {(Buffer | String)[]} list The frame to send\n   * @param {Function} [cb] Callback\n   * @private\n   */ sendFrame(list, cb) {\n        if (list.length === 2) {\n            this._socket.cork();\n            this._socket.write(list[0]);\n            this._socket.write(list[1], cb);\n            this._socket.uncork();\n        } else {\n            this._socket.write(list[0], cb);\n        }\n    }\n}\nmodule.exports = Sender;\n/**\n * Calls queued callbacks with an error.\n *\n * @param {Sender} sender The `Sender` instance\n * @param {Error} err The error to call the callbacks with\n * @param {Function} [cb] The first callback\n * @private\n */ function callCallbacks(sender, err, cb) {\n    if (typeof cb === \"function\") cb(err);\n    for(let i = 0; i < sender._queue.length; i++){\n        const params = sender._queue[i];\n        const callback = params[params.length - 1];\n        if (typeof callback === \"function\") callback(err);\n    }\n}\n/**\n * Handles a `Sender` error.\n *\n * @param {Sender} sender The `Sender` instance\n * @param {Error} err The error\n * @param {Function} [cb] The first pending callback\n * @private\n */ function onError(sender, err, cb) {\n    callCallbacks(sender, err, cb);\n    sender.onerror(err);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ws/lib/sender.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ws/lib/stream.js":
/*!***************************************!*\
  !*** ./node_modules/ws/lib/stream.js ***!
  \***************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/* eslint no-unused-vars: [\"error\", { \"varsIgnorePattern\": \"^WebSocket$\" }] */ \nconst WebSocket = __webpack_require__(/*! ./websocket */ \"(ssr)/./node_modules/ws/lib/websocket.js\");\nconst { Duplex } = __webpack_require__(/*! stream */ \"stream\");\n/**\n * Emits the `'close'` event on a stream.\n *\n * @param {Duplex} stream The stream.\n * @private\n */ function emitClose(stream) {\n    stream.emit(\"close\");\n}\n/**\n * The listener of the `'end'` event.\n *\n * @private\n */ function duplexOnEnd() {\n    if (!this.destroyed && this._writableState.finished) {\n        this.destroy();\n    }\n}\n/**\n * The listener of the `'error'` event.\n *\n * @param {Error} err The error\n * @private\n */ function duplexOnError(err) {\n    this.removeListener(\"error\", duplexOnError);\n    this.destroy();\n    if (this.listenerCount(\"error\") === 0) {\n        // Do not suppress the throwing behavior.\n        this.emit(\"error\", err);\n    }\n}\n/**\n * Wraps a `WebSocket` in a duplex stream.\n *\n * @param {WebSocket} ws The `WebSocket` to wrap\n * @param {Object} [options] The options for the `Duplex` constructor\n * @return {Duplex} The duplex stream\n * @public\n */ function createWebSocketStream(ws, options) {\n    let terminateOnDestroy = true;\n    const duplex = new Duplex({\n        ...options,\n        autoDestroy: false,\n        emitClose: false,\n        objectMode: false,\n        writableObjectMode: false\n    });\n    ws.on(\"message\", function message(msg, isBinary) {\n        const data = !isBinary && duplex._readableState.objectMode ? msg.toString() : msg;\n        if (!duplex.push(data)) ws.pause();\n    });\n    ws.once(\"error\", function error(err) {\n        if (duplex.destroyed) return;\n        // Prevent `ws.terminate()` from being called by `duplex._destroy()`.\n        //\n        // - If the `'error'` event is emitted before the `'open'` event, then\n        //   `ws.terminate()` is a noop as no socket is assigned.\n        // - Otherwise, the error is re-emitted by the listener of the `'error'`\n        //   event of the `Receiver` object. The listener already closes the\n        //   connection by calling `ws.close()`. This allows a close frame to be\n        //   sent to the other peer. If `ws.terminate()` is called right after this,\n        //   then the close frame might not be sent.\n        terminateOnDestroy = false;\n        duplex.destroy(err);\n    });\n    ws.once(\"close\", function close() {\n        if (duplex.destroyed) return;\n        duplex.push(null);\n    });\n    duplex._destroy = function(err, callback) {\n        if (ws.readyState === ws.CLOSED) {\n            callback(err);\n            process.nextTick(emitClose, duplex);\n            return;\n        }\n        let called = false;\n        ws.once(\"error\", function error(err) {\n            called = true;\n            callback(err);\n        });\n        ws.once(\"close\", function close() {\n            if (!called) callback(err);\n            process.nextTick(emitClose, duplex);\n        });\n        if (terminateOnDestroy) ws.terminate();\n    };\n    duplex._final = function(callback) {\n        if (ws.readyState === ws.CONNECTING) {\n            ws.once(\"open\", function open() {\n                duplex._final(callback);\n            });\n            return;\n        }\n        // If the value of the `_socket` property is `null` it means that `ws` is a\n        // client websocket and the handshake failed. In fact, when this happens, a\n        // socket is never assigned to the websocket. Wait for the `'error'` event\n        // that will be emitted by the websocket.\n        if (ws._socket === null) return;\n        if (ws._socket._writableState.finished) {\n            callback();\n            if (duplex._readableState.endEmitted) duplex.destroy();\n        } else {\n            ws._socket.once(\"finish\", function finish() {\n                // `duplex` is not destroyed here because the `'end'` event will be\n                // emitted on `duplex` after this `'finish'` event. The EOF signaling\n                // `null` chunk is, in fact, pushed when the websocket emits `'close'`.\n                callback();\n            });\n            ws.close();\n        }\n    };\n    duplex._read = function() {\n        if (ws.isPaused) ws.resume();\n    };\n    duplex._write = function(chunk, encoding, callback) {\n        if (ws.readyState === ws.CONNECTING) {\n            ws.once(\"open\", function open() {\n                duplex._write(chunk, encoding, callback);\n            });\n            return;\n        }\n        ws.send(chunk, callback);\n    };\n    duplex.on(\"end\", duplexOnEnd);\n    duplex.on(\"error\", duplexOnError);\n    return duplex;\n}\nmodule.exports = createWebSocketStream;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ws/lib/stream.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ws/lib/subprotocol.js":
/*!********************************************!*\
  !*** ./node_modules/ws/lib/subprotocol.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst { tokenChars } = __webpack_require__(/*! ./validation */ \"(ssr)/./node_modules/ws/lib/validation.js\");\n/**\n * Parses the `Sec-WebSocket-Protocol` header into a set of subprotocol names.\n *\n * @param {String} header The field value of the header\n * @return {Set} The subprotocol names\n * @public\n */ function parse(header) {\n    const protocols = new Set();\n    let start = -1;\n    let end = -1;\n    let i = 0;\n    for(i; i < header.length; i++){\n        const code = header.charCodeAt(i);\n        if (end === -1 && tokenChars[code] === 1) {\n            if (start === -1) start = i;\n        } else if (i !== 0 && (code === 0x20 /* ' ' */  || code === 0x09)) {\n            if (end === -1 && start !== -1) end = i;\n        } else if (code === 0x2c /* ',' */ ) {\n            if (start === -1) {\n                throw new SyntaxError(`Unexpected character at index ${i}`);\n            }\n            if (end === -1) end = i;\n            const protocol = header.slice(start, end);\n            if (protocols.has(protocol)) {\n                throw new SyntaxError(`The \"${protocol}\" subprotocol is duplicated`);\n            }\n            protocols.add(protocol);\n            start = end = -1;\n        } else {\n            throw new SyntaxError(`Unexpected character at index ${i}`);\n        }\n    }\n    if (start === -1 || end !== -1) {\n        throw new SyntaxError(\"Unexpected end of input\");\n    }\n    const protocol = header.slice(start, i);\n    if (protocols.has(protocol)) {\n        throw new SyntaxError(`The \"${protocol}\" subprotocol is duplicated`);\n    }\n    protocols.add(protocol);\n    return protocols;\n}\nmodule.exports = {\n    parse\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ws/lib/subprotocol.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ws/lib/validation.js":
/*!*******************************************!*\
  !*** ./node_modules/ws/lib/validation.js ***!
  \*******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst { isUtf8 } = __webpack_require__(/*! buffer */ \"buffer\");\nconst { hasBlob } = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/ws/lib/constants.js\");\n//\n// Allowed token characters:\n//\n// '!', '#', '$', '%', '&', ''', '*', '+', '-',\n// '.', 0-9, A-Z, '^', '_', '`', a-z, '|', '~'\n//\n// tokenChars[32] === 0 // ' '\n// tokenChars[33] === 1 // '!'\n// tokenChars[34] === 0 // '\"'\n// ...\n//\n// prettier-ignore\nconst tokenChars = [\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    1,\n    0,\n    1,\n    1,\n    1,\n    1,\n    1,\n    0,\n    0,\n    1,\n    1,\n    0,\n    1,\n    1,\n    0,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    0,\n    0,\n    0,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    0,\n    1,\n    0,\n    1,\n    0 // 112 - 127\n];\n/**\n * Checks if a status code is allowed in a close frame.\n *\n * @param {Number} code The status code\n * @return {Boolean} `true` if the status code is valid, else `false`\n * @public\n */ function isValidStatusCode(code) {\n    return code >= 1000 && code <= 1014 && code !== 1004 && code !== 1005 && code !== 1006 || code >= 3000 && code <= 4999;\n}\n/**\n * Checks if a given buffer contains only correct UTF-8.\n * Ported from https://www.cl.cam.ac.uk/%7Emgk25/ucs/utf8_check.c by\n * Markus Kuhn.\n *\n * @param {Buffer} buf The buffer to check\n * @return {Boolean} `true` if `buf` contains only correct UTF-8, else `false`\n * @public\n */ function _isValidUTF8(buf) {\n    const len = buf.length;\n    let i = 0;\n    while(i < len){\n        if ((buf[i] & 0x80) === 0) {\n            // 0xxxxxxx\n            i++;\n        } else if ((buf[i] & 0xe0) === 0xc0) {\n            // 110xxxxx 10xxxxxx\n            if (i + 1 === len || (buf[i + 1] & 0xc0) !== 0x80 || (buf[i] & 0xfe) === 0xc0 // Overlong\n            ) {\n                return false;\n            }\n            i += 2;\n        } else if ((buf[i] & 0xf0) === 0xe0) {\n            // 1110xxxx 10xxxxxx 10xxxxxx\n            if (i + 2 >= len || (buf[i + 1] & 0xc0) !== 0x80 || (buf[i + 2] & 0xc0) !== 0x80 || buf[i] === 0xe0 && (buf[i + 1] & 0xe0) === 0x80 || // Overlong\n            buf[i] === 0xed && (buf[i + 1] & 0xe0) === 0xa0 // Surrogate (U+D800 - U+DFFF)\n            ) {\n                return false;\n            }\n            i += 3;\n        } else if ((buf[i] & 0xf8) === 0xf0) {\n            // 11110xxx 10xxxxxx 10xxxxxx 10xxxxxx\n            if (i + 3 >= len || (buf[i + 1] & 0xc0) !== 0x80 || (buf[i + 2] & 0xc0) !== 0x80 || (buf[i + 3] & 0xc0) !== 0x80 || buf[i] === 0xf0 && (buf[i + 1] & 0xf0) === 0x80 || // Overlong\n            buf[i] === 0xf4 && buf[i + 1] > 0x8f || buf[i] > 0xf4 // > U+10FFFF\n            ) {\n                return false;\n            }\n            i += 4;\n        } else {\n            return false;\n        }\n    }\n    return true;\n}\n/**\n * Determines whether a value is a `Blob`.\n *\n * @param {*} value The value to be tested\n * @return {Boolean} `true` if `value` is a `Blob`, else `false`\n * @private\n */ function isBlob(value) {\n    return hasBlob && typeof value === \"object\" && typeof value.arrayBuffer === \"function\" && typeof value.type === \"string\" && typeof value.stream === \"function\" && (value[Symbol.toStringTag] === \"Blob\" || value[Symbol.toStringTag] === \"File\");\n}\nmodule.exports = {\n    isBlob,\n    isValidStatusCode,\n    isValidUTF8: _isValidUTF8,\n    tokenChars\n};\nif (isUtf8) {\n    module.exports.isValidUTF8 = function(buf) {\n        return buf.length < 24 ? _isValidUTF8(buf) : isUtf8(buf);\n    };\n} else if (!process.env.WS_NO_UTF_8_VALIDATE) {\n    try {\n        const isValidUTF8 = __webpack_require__(/*! utf-8-validate */ \"?66e9\");\n        module.exports.isValidUTF8 = function(buf) {\n            return buf.length < 32 ? _isValidUTF8(buf) : isValidUTF8(buf);\n        };\n    } catch (e) {\n    // Continue regardless of the error.\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ws/lib/validation.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ws/lib/websocket-server.js":
/*!*************************************************!*\
  !*** ./node_modules/ws/lib/websocket-server.js ***!
  \*************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/* eslint no-unused-vars: [\"error\", { \"varsIgnorePattern\": \"^Duplex$\", \"caughtErrors\": \"none\" }] */ \nconst EventEmitter = __webpack_require__(/*! events */ \"events\");\nconst http = __webpack_require__(/*! http */ \"http\");\nconst { Duplex } = __webpack_require__(/*! stream */ \"stream\");\nconst { createHash } = __webpack_require__(/*! crypto */ \"crypto\");\nconst extension = __webpack_require__(/*! ./extension */ \"(ssr)/./node_modules/ws/lib/extension.js\");\nconst PerMessageDeflate = __webpack_require__(/*! ./permessage-deflate */ \"(ssr)/./node_modules/ws/lib/permessage-deflate.js\");\nconst subprotocol = __webpack_require__(/*! ./subprotocol */ \"(ssr)/./node_modules/ws/lib/subprotocol.js\");\nconst WebSocket = __webpack_require__(/*! ./websocket */ \"(ssr)/./node_modules/ws/lib/websocket.js\");\nconst { GUID, kWebSocket } = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/ws/lib/constants.js\");\nconst keyRegex = /^[+/0-9A-Za-z]{22}==$/;\nconst RUNNING = 0;\nconst CLOSING = 1;\nconst CLOSED = 2;\n/**\n * Class representing a WebSocket server.\n *\n * @extends EventEmitter\n */ class WebSocketServer extends EventEmitter {\n    /**\n   * Create a `WebSocketServer` instance.\n   *\n   * @param {Object} options Configuration options\n   * @param {Boolean} [options.allowSynchronousEvents=true] Specifies whether\n   *     any of the `'message'`, `'ping'`, and `'pong'` events can be emitted\n   *     multiple times in the same tick\n   * @param {Boolean} [options.autoPong=true] Specifies whether or not to\n   *     automatically send a pong in response to a ping\n   * @param {Number} [options.backlog=511] The maximum length of the queue of\n   *     pending connections\n   * @param {Boolean} [options.clientTracking=true] Specifies whether or not to\n   *     track clients\n   * @param {Function} [options.handleProtocols] A hook to handle protocols\n   * @param {String} [options.host] The hostname where to bind the server\n   * @param {Number} [options.maxPayload=104857600] The maximum allowed message\n   *     size\n   * @param {Boolean} [options.noServer=false] Enable no server mode\n   * @param {String} [options.path] Accept only connections matching this path\n   * @param {(Boolean|Object)} [options.perMessageDeflate=false] Enable/disable\n   *     permessage-deflate\n   * @param {Number} [options.port] The port where to bind the server\n   * @param {(http.Server|https.Server)} [options.server] A pre-created HTTP/S\n   *     server to use\n   * @param {Boolean} [options.skipUTF8Validation=false] Specifies whether or\n   *     not to skip UTF-8 validation for text and close messages\n   * @param {Function} [options.verifyClient] A hook to reject connections\n   * @param {Function} [options.WebSocket=WebSocket] Specifies the `WebSocket`\n   *     class to use. It must be the `WebSocket` class or class that extends it\n   * @param {Function} [callback] A listener for the `listening` event\n   */ constructor(options, callback){\n        super();\n        options = {\n            allowSynchronousEvents: true,\n            autoPong: true,\n            maxPayload: 100 * 1024 * 1024,\n            skipUTF8Validation: false,\n            perMessageDeflate: false,\n            handleProtocols: null,\n            clientTracking: true,\n            verifyClient: null,\n            noServer: false,\n            backlog: null,\n            server: null,\n            host: null,\n            path: null,\n            port: null,\n            WebSocket,\n            ...options\n        };\n        if (options.port == null && !options.server && !options.noServer || options.port != null && (options.server || options.noServer) || options.server && options.noServer) {\n            throw new TypeError('One and only one of the \"port\", \"server\", or \"noServer\" options ' + \"must be specified\");\n        }\n        if (options.port != null) {\n            this._server = http.createServer((req, res)=>{\n                const body = http.STATUS_CODES[426];\n                res.writeHead(426, {\n                    \"Content-Length\": body.length,\n                    \"Content-Type\": \"text/plain\"\n                });\n                res.end(body);\n            });\n            this._server.listen(options.port, options.host, options.backlog, callback);\n        } else if (options.server) {\n            this._server = options.server;\n        }\n        if (this._server) {\n            const emitConnection = this.emit.bind(this, \"connection\");\n            this._removeListeners = addListeners(this._server, {\n                listening: this.emit.bind(this, \"listening\"),\n                error: this.emit.bind(this, \"error\"),\n                upgrade: (req, socket, head)=>{\n                    this.handleUpgrade(req, socket, head, emitConnection);\n                }\n            });\n        }\n        if (options.perMessageDeflate === true) options.perMessageDeflate = {};\n        if (options.clientTracking) {\n            this.clients = new Set();\n            this._shouldEmitClose = false;\n        }\n        this.options = options;\n        this._state = RUNNING;\n    }\n    /**\n   * Returns the bound address, the address family name, and port of the server\n   * as reported by the operating system if listening on an IP socket.\n   * If the server is listening on a pipe or UNIX domain socket, the name is\n   * returned as a string.\n   *\n   * @return {(Object|String|null)} The address of the server\n   * @public\n   */ address() {\n        if (this.options.noServer) {\n            throw new Error('The server is operating in \"noServer\" mode');\n        }\n        if (!this._server) return null;\n        return this._server.address();\n    }\n    /**\n   * Stop the server from accepting new connections and emit the `'close'` event\n   * when all existing connections are closed.\n   *\n   * @param {Function} [cb] A one-time listener for the `'close'` event\n   * @public\n   */ close(cb) {\n        if (this._state === CLOSED) {\n            if (cb) {\n                this.once(\"close\", ()=>{\n                    cb(new Error(\"The server is not running\"));\n                });\n            }\n            process.nextTick(emitClose, this);\n            return;\n        }\n        if (cb) this.once(\"close\", cb);\n        if (this._state === CLOSING) return;\n        this._state = CLOSING;\n        if (this.options.noServer || this.options.server) {\n            if (this._server) {\n                this._removeListeners();\n                this._removeListeners = this._server = null;\n            }\n            if (this.clients) {\n                if (!this.clients.size) {\n                    process.nextTick(emitClose, this);\n                } else {\n                    this._shouldEmitClose = true;\n                }\n            } else {\n                process.nextTick(emitClose, this);\n            }\n        } else {\n            const server = this._server;\n            this._removeListeners();\n            this._removeListeners = this._server = null;\n            //\n            // The HTTP/S server was created internally. Close it, and rely on its\n            // `'close'` event.\n            //\n            server.close(()=>{\n                emitClose(this);\n            });\n        }\n    }\n    /**\n   * See if a given request should be handled by this server instance.\n   *\n   * @param {http.IncomingMessage} req Request object to inspect\n   * @return {Boolean} `true` if the request is valid, else `false`\n   * @public\n   */ shouldHandle(req) {\n        if (this.options.path) {\n            const index = req.url.indexOf(\"?\");\n            const pathname = index !== -1 ? req.url.slice(0, index) : req.url;\n            if (pathname !== this.options.path) return false;\n        }\n        return true;\n    }\n    /**\n   * Handle a HTTP Upgrade request.\n   *\n   * @param {http.IncomingMessage} req The request object\n   * @param {Duplex} socket The network socket between the server and client\n   * @param {Buffer} head The first packet of the upgraded stream\n   * @param {Function} cb Callback\n   * @public\n   */ handleUpgrade(req, socket, head, cb) {\n        socket.on(\"error\", socketOnError);\n        const key = req.headers[\"sec-websocket-key\"];\n        const upgrade = req.headers.upgrade;\n        const version = +req.headers[\"sec-websocket-version\"];\n        if (req.method !== \"GET\") {\n            const message = \"Invalid HTTP method\";\n            abortHandshakeOrEmitwsClientError(this, req, socket, 405, message);\n            return;\n        }\n        if (upgrade === undefined || upgrade.toLowerCase() !== \"websocket\") {\n            const message = \"Invalid Upgrade header\";\n            abortHandshakeOrEmitwsClientError(this, req, socket, 400, message);\n            return;\n        }\n        if (key === undefined || !keyRegex.test(key)) {\n            const message = \"Missing or invalid Sec-WebSocket-Key header\";\n            abortHandshakeOrEmitwsClientError(this, req, socket, 400, message);\n            return;\n        }\n        if (version !== 13 && version !== 8) {\n            const message = \"Missing or invalid Sec-WebSocket-Version header\";\n            abortHandshakeOrEmitwsClientError(this, req, socket, 400, message, {\n                \"Sec-WebSocket-Version\": \"13, 8\"\n            });\n            return;\n        }\n        if (!this.shouldHandle(req)) {\n            abortHandshake(socket, 400);\n            return;\n        }\n        const secWebSocketProtocol = req.headers[\"sec-websocket-protocol\"];\n        let protocols = new Set();\n        if (secWebSocketProtocol !== undefined) {\n            try {\n                protocols = subprotocol.parse(secWebSocketProtocol);\n            } catch (err) {\n                const message = \"Invalid Sec-WebSocket-Protocol header\";\n                abortHandshakeOrEmitwsClientError(this, req, socket, 400, message);\n                return;\n            }\n        }\n        const secWebSocketExtensions = req.headers[\"sec-websocket-extensions\"];\n        const extensions = {};\n        if (this.options.perMessageDeflate && secWebSocketExtensions !== undefined) {\n            const perMessageDeflate = new PerMessageDeflate(this.options.perMessageDeflate, true, this.options.maxPayload);\n            try {\n                const offers = extension.parse(secWebSocketExtensions);\n                if (offers[PerMessageDeflate.extensionName]) {\n                    perMessageDeflate.accept(offers[PerMessageDeflate.extensionName]);\n                    extensions[PerMessageDeflate.extensionName] = perMessageDeflate;\n                }\n            } catch (err) {\n                const message = \"Invalid or unacceptable Sec-WebSocket-Extensions header\";\n                abortHandshakeOrEmitwsClientError(this, req, socket, 400, message);\n                return;\n            }\n        }\n        //\n        // Optionally call external client verification handler.\n        //\n        if (this.options.verifyClient) {\n            const info = {\n                origin: req.headers[`${version === 8 ? \"sec-websocket-origin\" : \"origin\"}`],\n                secure: !!(req.socket.authorized || req.socket.encrypted),\n                req\n            };\n            if (this.options.verifyClient.length === 2) {\n                this.options.verifyClient(info, (verified, code, message, headers)=>{\n                    if (!verified) {\n                        return abortHandshake(socket, code || 401, message, headers);\n                    }\n                    this.completeUpgrade(extensions, key, protocols, req, socket, head, cb);\n                });\n                return;\n            }\n            if (!this.options.verifyClient(info)) return abortHandshake(socket, 401);\n        }\n        this.completeUpgrade(extensions, key, protocols, req, socket, head, cb);\n    }\n    /**\n   * Upgrade the connection to WebSocket.\n   *\n   * @param {Object} extensions The accepted extensions\n   * @param {String} key The value of the `Sec-WebSocket-Key` header\n   * @param {Set} protocols The subprotocols\n   * @param {http.IncomingMessage} req The request object\n   * @param {Duplex} socket The network socket between the server and client\n   * @param {Buffer} head The first packet of the upgraded stream\n   * @param {Function} cb Callback\n   * @throws {Error} If called more than once with the same socket\n   * @private\n   */ completeUpgrade(extensions, key, protocols, req, socket, head, cb) {\n        //\n        // Destroy the socket if the client has already sent a FIN packet.\n        //\n        if (!socket.readable || !socket.writable) return socket.destroy();\n        if (socket[kWebSocket]) {\n            throw new Error(\"server.handleUpgrade() was called more than once with the same \" + \"socket, possibly due to a misconfiguration\");\n        }\n        if (this._state > RUNNING) return abortHandshake(socket, 503);\n        const digest = createHash(\"sha1\").update(key + GUID).digest(\"base64\");\n        const headers = [\n            \"HTTP/1.1 101 Switching Protocols\",\n            \"Upgrade: websocket\",\n            \"Connection: Upgrade\",\n            `Sec-WebSocket-Accept: ${digest}`\n        ];\n        const ws = new this.options.WebSocket(null, undefined, this.options);\n        if (protocols.size) {\n            //\n            // Optionally call external protocol selection handler.\n            //\n            const protocol = this.options.handleProtocols ? this.options.handleProtocols(protocols, req) : protocols.values().next().value;\n            if (protocol) {\n                headers.push(`Sec-WebSocket-Protocol: ${protocol}`);\n                ws._protocol = protocol;\n            }\n        }\n        if (extensions[PerMessageDeflate.extensionName]) {\n            const params = extensions[PerMessageDeflate.extensionName].params;\n            const value = extension.format({\n                [PerMessageDeflate.extensionName]: [\n                    params\n                ]\n            });\n            headers.push(`Sec-WebSocket-Extensions: ${value}`);\n            ws._extensions = extensions;\n        }\n        //\n        // Allow external modification/inspection of handshake headers.\n        //\n        this.emit(\"headers\", headers, req);\n        socket.write(headers.concat(\"\\r\\n\").join(\"\\r\\n\"));\n        socket.removeListener(\"error\", socketOnError);\n        ws.setSocket(socket, head, {\n            allowSynchronousEvents: this.options.allowSynchronousEvents,\n            maxPayload: this.options.maxPayload,\n            skipUTF8Validation: this.options.skipUTF8Validation\n        });\n        if (this.clients) {\n            this.clients.add(ws);\n            ws.on(\"close\", ()=>{\n                this.clients.delete(ws);\n                if (this._shouldEmitClose && !this.clients.size) {\n                    process.nextTick(emitClose, this);\n                }\n            });\n        }\n        cb(ws, req);\n    }\n}\nmodule.exports = WebSocketServer;\n/**\n * Add event listeners on an `EventEmitter` using a map of <event, listener>\n * pairs.\n *\n * @param {EventEmitter} server The event emitter\n * @param {Object.<String, Function>} map The listeners to add\n * @return {Function} A function that will remove the added listeners when\n *     called\n * @private\n */ function addListeners(server, map) {\n    for (const event of Object.keys(map))server.on(event, map[event]);\n    return function removeListeners() {\n        for (const event of Object.keys(map)){\n            server.removeListener(event, map[event]);\n        }\n    };\n}\n/**\n * Emit a `'close'` event on an `EventEmitter`.\n *\n * @param {EventEmitter} server The event emitter\n * @private\n */ function emitClose(server) {\n    server._state = CLOSED;\n    server.emit(\"close\");\n}\n/**\n * Handle socket errors.\n *\n * @private\n */ function socketOnError() {\n    this.destroy();\n}\n/**\n * Close the connection when preconditions are not fulfilled.\n *\n * @param {Duplex} socket The socket of the upgrade request\n * @param {Number} code The HTTP response status code\n * @param {String} [message] The HTTP response body\n * @param {Object} [headers] Additional HTTP response headers\n * @private\n */ function abortHandshake(socket, code, message, headers) {\n    //\n    // The socket is writable unless the user destroyed or ended it before calling\n    // `server.handleUpgrade()` or in the `verifyClient` function, which is a user\n    // error. Handling this does not make much sense as the worst that can happen\n    // is that some of the data written by the user might be discarded due to the\n    // call to `socket.end()` below, which triggers an `'error'` event that in\n    // turn causes the socket to be destroyed.\n    //\n    message = message || http.STATUS_CODES[code];\n    headers = {\n        Connection: \"close\",\n        \"Content-Type\": \"text/html\",\n        \"Content-Length\": Buffer.byteLength(message),\n        ...headers\n    };\n    socket.once(\"finish\", socket.destroy);\n    socket.end(`HTTP/1.1 ${code} ${http.STATUS_CODES[code]}\\r\\n` + Object.keys(headers).map((h)=>`${h}: ${headers[h]}`).join(\"\\r\\n\") + \"\\r\\n\\r\\n\" + message);\n}\n/**\n * Emit a `'wsClientError'` event on a `WebSocketServer` if there is at least\n * one listener for it, otherwise call `abortHandshake()`.\n *\n * @param {WebSocketServer} server The WebSocket server\n * @param {http.IncomingMessage} req The request object\n * @param {Duplex} socket The socket of the upgrade request\n * @param {Number} code The HTTP response status code\n * @param {String} message The HTTP response body\n * @param {Object} [headers] The HTTP response headers\n * @private\n */ function abortHandshakeOrEmitwsClientError(server, req, socket, code, message, headers) {\n    if (server.listenerCount(\"wsClientError\")) {\n        const err = new Error(message);\n        Error.captureStackTrace(err, abortHandshakeOrEmitwsClientError);\n        server.emit(\"wsClientError\", err, socket, req);\n    } else {\n        abortHandshake(socket, code, message, headers);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ws/lib/websocket-server.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ws/lib/websocket.js":
/*!******************************************!*\
  !*** ./node_modules/ws/lib/websocket.js ***!
  \******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/* eslint no-unused-vars: [\"error\", { \"varsIgnorePattern\": \"^Duplex|Readable$\", \"caughtErrors\": \"none\" }] */ \nconst EventEmitter = __webpack_require__(/*! events */ \"events\");\nconst https = __webpack_require__(/*! https */ \"https\");\nconst http = __webpack_require__(/*! http */ \"http\");\nconst net = __webpack_require__(/*! net */ \"net\");\nconst tls = __webpack_require__(/*! tls */ \"tls\");\nconst { randomBytes, createHash } = __webpack_require__(/*! crypto */ \"crypto\");\nconst { Duplex, Readable } = __webpack_require__(/*! stream */ \"stream\");\nconst { URL } = __webpack_require__(/*! url */ \"url\");\nconst PerMessageDeflate = __webpack_require__(/*! ./permessage-deflate */ \"(ssr)/./node_modules/ws/lib/permessage-deflate.js\");\nconst Receiver = __webpack_require__(/*! ./receiver */ \"(ssr)/./node_modules/ws/lib/receiver.js\");\nconst Sender = __webpack_require__(/*! ./sender */ \"(ssr)/./node_modules/ws/lib/sender.js\");\nconst { isBlob } = __webpack_require__(/*! ./validation */ \"(ssr)/./node_modules/ws/lib/validation.js\");\nconst { BINARY_TYPES, EMPTY_BUFFER, GUID, kForOnEventAttribute, kListener, kStatusCode, kWebSocket, NOOP } = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/ws/lib/constants.js\");\nconst { EventTarget: { addEventListener, removeEventListener } } = __webpack_require__(/*! ./event-target */ \"(ssr)/./node_modules/ws/lib/event-target.js\");\nconst { format, parse } = __webpack_require__(/*! ./extension */ \"(ssr)/./node_modules/ws/lib/extension.js\");\nconst { toBuffer } = __webpack_require__(/*! ./buffer-util */ \"(ssr)/./node_modules/ws/lib/buffer-util.js\");\nconst closeTimeout = 30 * 1000;\nconst kAborted = Symbol(\"kAborted\");\nconst protocolVersions = [\n    8,\n    13\n];\nconst readyStates = [\n    \"CONNECTING\",\n    \"OPEN\",\n    \"CLOSING\",\n    \"CLOSED\"\n];\nconst subprotocolRegex = /^[!#$%&'*+\\-.0-9A-Z^_`|a-z~]+$/;\n/**\n * Class representing a WebSocket.\n *\n * @extends EventEmitter\n */ class WebSocket extends EventEmitter {\n    /**\n   * Create a new `WebSocket`.\n   *\n   * @param {(String|URL)} address The URL to which to connect\n   * @param {(String|String[])} [protocols] The subprotocols\n   * @param {Object} [options] Connection options\n   */ constructor(address, protocols, options){\n        super();\n        this._binaryType = BINARY_TYPES[0];\n        this._closeCode = 1006;\n        this._closeFrameReceived = false;\n        this._closeFrameSent = false;\n        this._closeMessage = EMPTY_BUFFER;\n        this._closeTimer = null;\n        this._errorEmitted = false;\n        this._extensions = {};\n        this._paused = false;\n        this._protocol = \"\";\n        this._readyState = WebSocket.CONNECTING;\n        this._receiver = null;\n        this._sender = null;\n        this._socket = null;\n        if (address !== null) {\n            this._bufferedAmount = 0;\n            this._isServer = false;\n            this._redirects = 0;\n            if (protocols === undefined) {\n                protocols = [];\n            } else if (!Array.isArray(protocols)) {\n                if (typeof protocols === \"object\" && protocols !== null) {\n                    options = protocols;\n                    protocols = [];\n                } else {\n                    protocols = [\n                        protocols\n                    ];\n                }\n            }\n            initAsClient(this, address, protocols, options);\n        } else {\n            this._autoPong = options.autoPong;\n            this._isServer = true;\n        }\n    }\n    /**\n   * For historical reasons, the custom \"nodebuffer\" type is used by the default\n   * instead of \"blob\".\n   *\n   * @type {String}\n   */ get binaryType() {\n        return this._binaryType;\n    }\n    set binaryType(type) {\n        if (!BINARY_TYPES.includes(type)) return;\n        this._binaryType = type;\n        //\n        // Allow to change `binaryType` on the fly.\n        //\n        if (this._receiver) this._receiver._binaryType = type;\n    }\n    /**\n   * @type {Number}\n   */ get bufferedAmount() {\n        if (!this._socket) return this._bufferedAmount;\n        return this._socket._writableState.length + this._sender._bufferedBytes;\n    }\n    /**\n   * @type {String}\n   */ get extensions() {\n        return Object.keys(this._extensions).join();\n    }\n    /**\n   * @type {Boolean}\n   */ get isPaused() {\n        return this._paused;\n    }\n    /**\n   * @type {Function}\n   */ /* istanbul ignore next */ get onclose() {\n        return null;\n    }\n    /**\n   * @type {Function}\n   */ /* istanbul ignore next */ get onerror() {\n        return null;\n    }\n    /**\n   * @type {Function}\n   */ /* istanbul ignore next */ get onopen() {\n        return null;\n    }\n    /**\n   * @type {Function}\n   */ /* istanbul ignore next */ get onmessage() {\n        return null;\n    }\n    /**\n   * @type {String}\n   */ get protocol() {\n        return this._protocol;\n    }\n    /**\n   * @type {Number}\n   */ get readyState() {\n        return this._readyState;\n    }\n    /**\n   * @type {String}\n   */ get url() {\n        return this._url;\n    }\n    /**\n   * Set up the socket and the internal resources.\n   *\n   * @param {Duplex} socket The network socket between the server and client\n   * @param {Buffer} head The first packet of the upgraded stream\n   * @param {Object} options Options object\n   * @param {Boolean} [options.allowSynchronousEvents=false] Specifies whether\n   *     any of the `'message'`, `'ping'`, and `'pong'` events can be emitted\n   *     multiple times in the same tick\n   * @param {Function} [options.generateMask] The function used to generate the\n   *     masking key\n   * @param {Number} [options.maxPayload=0] The maximum allowed message size\n   * @param {Boolean} [options.skipUTF8Validation=false] Specifies whether or\n   *     not to skip UTF-8 validation for text and close messages\n   * @private\n   */ setSocket(socket, head, options) {\n        const receiver = new Receiver({\n            allowSynchronousEvents: options.allowSynchronousEvents,\n            binaryType: this.binaryType,\n            extensions: this._extensions,\n            isServer: this._isServer,\n            maxPayload: options.maxPayload,\n            skipUTF8Validation: options.skipUTF8Validation\n        });\n        const sender = new Sender(socket, this._extensions, options.generateMask);\n        this._receiver = receiver;\n        this._sender = sender;\n        this._socket = socket;\n        receiver[kWebSocket] = this;\n        sender[kWebSocket] = this;\n        socket[kWebSocket] = this;\n        receiver.on(\"conclude\", receiverOnConclude);\n        receiver.on(\"drain\", receiverOnDrain);\n        receiver.on(\"error\", receiverOnError);\n        receiver.on(\"message\", receiverOnMessage);\n        receiver.on(\"ping\", receiverOnPing);\n        receiver.on(\"pong\", receiverOnPong);\n        sender.onerror = senderOnError;\n        //\n        // These methods may not be available if `socket` is just a `Duplex`.\n        //\n        if (socket.setTimeout) socket.setTimeout(0);\n        if (socket.setNoDelay) socket.setNoDelay();\n        if (head.length > 0) socket.unshift(head);\n        socket.on(\"close\", socketOnClose);\n        socket.on(\"data\", socketOnData);\n        socket.on(\"end\", socketOnEnd);\n        socket.on(\"error\", socketOnError);\n        this._readyState = WebSocket.OPEN;\n        this.emit(\"open\");\n    }\n    /**\n   * Emit the `'close'` event.\n   *\n   * @private\n   */ emitClose() {\n        if (!this._socket) {\n            this._readyState = WebSocket.CLOSED;\n            this.emit(\"close\", this._closeCode, this._closeMessage);\n            return;\n        }\n        if (this._extensions[PerMessageDeflate.extensionName]) {\n            this._extensions[PerMessageDeflate.extensionName].cleanup();\n        }\n        this._receiver.removeAllListeners();\n        this._readyState = WebSocket.CLOSED;\n        this.emit(\"close\", this._closeCode, this._closeMessage);\n    }\n    /**\n   * Start a closing handshake.\n   *\n   *          +----------+   +-----------+   +----------+\n   *     - - -|ws.close()|-->|close frame|-->|ws.close()|- - -\n   *    |     +----------+   +-----------+   +----------+     |\n   *          +----------+   +-----------+         |\n   * CLOSING  |ws.close()|<--|close frame|<--+-----+       CLOSING\n   *          +----------+   +-----------+   |\n   *    |           |                        |   +---+        |\n   *                +------------------------+-->|fin| - - - -\n   *    |         +---+                      |   +---+\n   *     - - - - -|fin|<---------------------+\n   *              +---+\n   *\n   * @param {Number} [code] Status code explaining why the connection is closing\n   * @param {(String|Buffer)} [data] The reason why the connection is\n   *     closing\n   * @public\n   */ close(code, data) {\n        if (this.readyState === WebSocket.CLOSED) return;\n        if (this.readyState === WebSocket.CONNECTING) {\n            const msg = \"WebSocket was closed before the connection was established\";\n            abortHandshake(this, this._req, msg);\n            return;\n        }\n        if (this.readyState === WebSocket.CLOSING) {\n            if (this._closeFrameSent && (this._closeFrameReceived || this._receiver._writableState.errorEmitted)) {\n                this._socket.end();\n            }\n            return;\n        }\n        this._readyState = WebSocket.CLOSING;\n        this._sender.close(code, data, !this._isServer, (err)=>{\n            //\n            // This error is handled by the `'error'` listener on the socket. We only\n            // want to know if the close frame has been sent here.\n            //\n            if (err) return;\n            this._closeFrameSent = true;\n            if (this._closeFrameReceived || this._receiver._writableState.errorEmitted) {\n                this._socket.end();\n            }\n        });\n        setCloseTimer(this);\n    }\n    /**\n   * Pause the socket.\n   *\n   * @public\n   */ pause() {\n        if (this.readyState === WebSocket.CONNECTING || this.readyState === WebSocket.CLOSED) {\n            return;\n        }\n        this._paused = true;\n        this._socket.pause();\n    }\n    /**\n   * Send a ping.\n   *\n   * @param {*} [data] The data to send\n   * @param {Boolean} [mask] Indicates whether or not to mask `data`\n   * @param {Function} [cb] Callback which is executed when the ping is sent\n   * @public\n   */ ping(data, mask, cb) {\n        if (this.readyState === WebSocket.CONNECTING) {\n            throw new Error(\"WebSocket is not open: readyState 0 (CONNECTING)\");\n        }\n        if (typeof data === \"function\") {\n            cb = data;\n            data = mask = undefined;\n        } else if (typeof mask === \"function\") {\n            cb = mask;\n            mask = undefined;\n        }\n        if (typeof data === \"number\") data = data.toString();\n        if (this.readyState !== WebSocket.OPEN) {\n            sendAfterClose(this, data, cb);\n            return;\n        }\n        if (mask === undefined) mask = !this._isServer;\n        this._sender.ping(data || EMPTY_BUFFER, mask, cb);\n    }\n    /**\n   * Send a pong.\n   *\n   * @param {*} [data] The data to send\n   * @param {Boolean} [mask] Indicates whether or not to mask `data`\n   * @param {Function} [cb] Callback which is executed when the pong is sent\n   * @public\n   */ pong(data, mask, cb) {\n        if (this.readyState === WebSocket.CONNECTING) {\n            throw new Error(\"WebSocket is not open: readyState 0 (CONNECTING)\");\n        }\n        if (typeof data === \"function\") {\n            cb = data;\n            data = mask = undefined;\n        } else if (typeof mask === \"function\") {\n            cb = mask;\n            mask = undefined;\n        }\n        if (typeof data === \"number\") data = data.toString();\n        if (this.readyState !== WebSocket.OPEN) {\n            sendAfterClose(this, data, cb);\n            return;\n        }\n        if (mask === undefined) mask = !this._isServer;\n        this._sender.pong(data || EMPTY_BUFFER, mask, cb);\n    }\n    /**\n   * Resume the socket.\n   *\n   * @public\n   */ resume() {\n        if (this.readyState === WebSocket.CONNECTING || this.readyState === WebSocket.CLOSED) {\n            return;\n        }\n        this._paused = false;\n        if (!this._receiver._writableState.needDrain) this._socket.resume();\n    }\n    /**\n   * Send a data message.\n   *\n   * @param {*} data The message to send\n   * @param {Object} [options] Options object\n   * @param {Boolean} [options.binary] Specifies whether `data` is binary or\n   *     text\n   * @param {Boolean} [options.compress] Specifies whether or not to compress\n   *     `data`\n   * @param {Boolean} [options.fin=true] Specifies whether the fragment is the\n   *     last one\n   * @param {Boolean} [options.mask] Specifies whether or not to mask `data`\n   * @param {Function} [cb] Callback which is executed when data is written out\n   * @public\n   */ send(data, options, cb) {\n        if (this.readyState === WebSocket.CONNECTING) {\n            throw new Error(\"WebSocket is not open: readyState 0 (CONNECTING)\");\n        }\n        if (typeof options === \"function\") {\n            cb = options;\n            options = {};\n        }\n        if (typeof data === \"number\") data = data.toString();\n        if (this.readyState !== WebSocket.OPEN) {\n            sendAfterClose(this, data, cb);\n            return;\n        }\n        const opts = {\n            binary: typeof data !== \"string\",\n            mask: !this._isServer,\n            compress: true,\n            fin: true,\n            ...options\n        };\n        if (!this._extensions[PerMessageDeflate.extensionName]) {\n            opts.compress = false;\n        }\n        this._sender.send(data || EMPTY_BUFFER, opts, cb);\n    }\n    /**\n   * Forcibly close the connection.\n   *\n   * @public\n   */ terminate() {\n        if (this.readyState === WebSocket.CLOSED) return;\n        if (this.readyState === WebSocket.CONNECTING) {\n            const msg = \"WebSocket was closed before the connection was established\";\n            abortHandshake(this, this._req, msg);\n            return;\n        }\n        if (this._socket) {\n            this._readyState = WebSocket.CLOSING;\n            this._socket.destroy();\n        }\n    }\n}\n/**\n * @constant {Number} CONNECTING\n * @memberof WebSocket\n */ Object.defineProperty(WebSocket, \"CONNECTING\", {\n    enumerable: true,\n    value: readyStates.indexOf(\"CONNECTING\")\n});\n/**\n * @constant {Number} CONNECTING\n * @memberof WebSocket.prototype\n */ Object.defineProperty(WebSocket.prototype, \"CONNECTING\", {\n    enumerable: true,\n    value: readyStates.indexOf(\"CONNECTING\")\n});\n/**\n * @constant {Number} OPEN\n * @memberof WebSocket\n */ Object.defineProperty(WebSocket, \"OPEN\", {\n    enumerable: true,\n    value: readyStates.indexOf(\"OPEN\")\n});\n/**\n * @constant {Number} OPEN\n * @memberof WebSocket.prototype\n */ Object.defineProperty(WebSocket.prototype, \"OPEN\", {\n    enumerable: true,\n    value: readyStates.indexOf(\"OPEN\")\n});\n/**\n * @constant {Number} CLOSING\n * @memberof WebSocket\n */ Object.defineProperty(WebSocket, \"CLOSING\", {\n    enumerable: true,\n    value: readyStates.indexOf(\"CLOSING\")\n});\n/**\n * @constant {Number} CLOSING\n * @memberof WebSocket.prototype\n */ Object.defineProperty(WebSocket.prototype, \"CLOSING\", {\n    enumerable: true,\n    value: readyStates.indexOf(\"CLOSING\")\n});\n/**\n * @constant {Number} CLOSED\n * @memberof WebSocket\n */ Object.defineProperty(WebSocket, \"CLOSED\", {\n    enumerable: true,\n    value: readyStates.indexOf(\"CLOSED\")\n});\n/**\n * @constant {Number} CLOSED\n * @memberof WebSocket.prototype\n */ Object.defineProperty(WebSocket.prototype, \"CLOSED\", {\n    enumerable: true,\n    value: readyStates.indexOf(\"CLOSED\")\n});\n[\n    \"binaryType\",\n    \"bufferedAmount\",\n    \"extensions\",\n    \"isPaused\",\n    \"protocol\",\n    \"readyState\",\n    \"url\"\n].forEach((property)=>{\n    Object.defineProperty(WebSocket.prototype, property, {\n        enumerable: true\n    });\n});\n//\n// Add the `onopen`, `onerror`, `onclose`, and `onmessage` attributes.\n// See https://html.spec.whatwg.org/multipage/comms.html#the-websocket-interface\n//\n[\n    \"open\",\n    \"error\",\n    \"close\",\n    \"message\"\n].forEach((method)=>{\n    Object.defineProperty(WebSocket.prototype, `on${method}`, {\n        enumerable: true,\n        get () {\n            for (const listener of this.listeners(method)){\n                if (listener[kForOnEventAttribute]) return listener[kListener];\n            }\n            return null;\n        },\n        set (handler) {\n            for (const listener of this.listeners(method)){\n                if (listener[kForOnEventAttribute]) {\n                    this.removeListener(method, listener);\n                    break;\n                }\n            }\n            if (typeof handler !== \"function\") return;\n            this.addEventListener(method, handler, {\n                [kForOnEventAttribute]: true\n            });\n        }\n    });\n});\nWebSocket.prototype.addEventListener = addEventListener;\nWebSocket.prototype.removeEventListener = removeEventListener;\nmodule.exports = WebSocket;\n/**\n * Initialize a WebSocket client.\n *\n * @param {WebSocket} websocket The client to initialize\n * @param {(String|URL)} address The URL to which to connect\n * @param {Array} protocols The subprotocols\n * @param {Object} [options] Connection options\n * @param {Boolean} [options.allowSynchronousEvents=true] Specifies whether any\n *     of the `'message'`, `'ping'`, and `'pong'` events can be emitted multiple\n *     times in the same tick\n * @param {Boolean} [options.autoPong=true] Specifies whether or not to\n *     automatically send a pong in response to a ping\n * @param {Function} [options.finishRequest] A function which can be used to\n *     customize the headers of each http request before it is sent\n * @param {Boolean} [options.followRedirects=false] Whether or not to follow\n *     redirects\n * @param {Function} [options.generateMask] The function used to generate the\n *     masking key\n * @param {Number} [options.handshakeTimeout] Timeout in milliseconds for the\n *     handshake request\n * @param {Number} [options.maxPayload=104857600] The maximum allowed message\n *     size\n * @param {Number} [options.maxRedirects=10] The maximum number of redirects\n *     allowed\n * @param {String} [options.origin] Value of the `Origin` or\n *     `Sec-WebSocket-Origin` header\n * @param {(Boolean|Object)} [options.perMessageDeflate=true] Enable/disable\n *     permessage-deflate\n * @param {Number} [options.protocolVersion=13] Value of the\n *     `Sec-WebSocket-Version` header\n * @param {Boolean} [options.skipUTF8Validation=false] Specifies whether or\n *     not to skip UTF-8 validation for text and close messages\n * @private\n */ function initAsClient(websocket, address, protocols, options) {\n    const opts = {\n        allowSynchronousEvents: true,\n        autoPong: true,\n        protocolVersion: protocolVersions[1],\n        maxPayload: 100 * 1024 * 1024,\n        skipUTF8Validation: false,\n        perMessageDeflate: true,\n        followRedirects: false,\n        maxRedirects: 10,\n        ...options,\n        socketPath: undefined,\n        hostname: undefined,\n        protocol: undefined,\n        timeout: undefined,\n        method: \"GET\",\n        host: undefined,\n        path: undefined,\n        port: undefined\n    };\n    websocket._autoPong = opts.autoPong;\n    if (!protocolVersions.includes(opts.protocolVersion)) {\n        throw new RangeError(`Unsupported protocol version: ${opts.protocolVersion} ` + `(supported versions: ${protocolVersions.join(\", \")})`);\n    }\n    let parsedUrl;\n    if (address instanceof URL) {\n        parsedUrl = address;\n    } else {\n        try {\n            parsedUrl = new URL(address);\n        } catch (e) {\n            throw new SyntaxError(`Invalid URL: ${address}`);\n        }\n    }\n    if (parsedUrl.protocol === \"http:\") {\n        parsedUrl.protocol = \"ws:\";\n    } else if (parsedUrl.protocol === \"https:\") {\n        parsedUrl.protocol = \"wss:\";\n    }\n    websocket._url = parsedUrl.href;\n    const isSecure = parsedUrl.protocol === \"wss:\";\n    const isIpcUrl = parsedUrl.protocol === \"ws+unix:\";\n    let invalidUrlMessage;\n    if (parsedUrl.protocol !== \"ws:\" && !isSecure && !isIpcUrl) {\n        invalidUrlMessage = 'The URL\\'s protocol must be one of \"ws:\", \"wss:\", ' + '\"http:\", \"https:\", or \"ws+unix:\"';\n    } else if (isIpcUrl && !parsedUrl.pathname) {\n        invalidUrlMessage = \"The URL's pathname is empty\";\n    } else if (parsedUrl.hash) {\n        invalidUrlMessage = \"The URL contains a fragment identifier\";\n    }\n    if (invalidUrlMessage) {\n        const err = new SyntaxError(invalidUrlMessage);\n        if (websocket._redirects === 0) {\n            throw err;\n        } else {\n            emitErrorAndClose(websocket, err);\n            return;\n        }\n    }\n    const defaultPort = isSecure ? 443 : 80;\n    const key = randomBytes(16).toString(\"base64\");\n    const request = isSecure ? https.request : http.request;\n    const protocolSet = new Set();\n    let perMessageDeflate;\n    opts.createConnection = opts.createConnection || (isSecure ? tlsConnect : netConnect);\n    opts.defaultPort = opts.defaultPort || defaultPort;\n    opts.port = parsedUrl.port || defaultPort;\n    opts.host = parsedUrl.hostname.startsWith(\"[\") ? parsedUrl.hostname.slice(1, -1) : parsedUrl.hostname;\n    opts.headers = {\n        ...opts.headers,\n        \"Sec-WebSocket-Version\": opts.protocolVersion,\n        \"Sec-WebSocket-Key\": key,\n        Connection: \"Upgrade\",\n        Upgrade: \"websocket\"\n    };\n    opts.path = parsedUrl.pathname + parsedUrl.search;\n    opts.timeout = opts.handshakeTimeout;\n    if (opts.perMessageDeflate) {\n        perMessageDeflate = new PerMessageDeflate(opts.perMessageDeflate !== true ? opts.perMessageDeflate : {}, false, opts.maxPayload);\n        opts.headers[\"Sec-WebSocket-Extensions\"] = format({\n            [PerMessageDeflate.extensionName]: perMessageDeflate.offer()\n        });\n    }\n    if (protocols.length) {\n        for (const protocol of protocols){\n            if (typeof protocol !== \"string\" || !subprotocolRegex.test(protocol) || protocolSet.has(protocol)) {\n                throw new SyntaxError(\"An invalid or duplicated subprotocol was specified\");\n            }\n            protocolSet.add(protocol);\n        }\n        opts.headers[\"Sec-WebSocket-Protocol\"] = protocols.join(\",\");\n    }\n    if (opts.origin) {\n        if (opts.protocolVersion < 13) {\n            opts.headers[\"Sec-WebSocket-Origin\"] = opts.origin;\n        } else {\n            opts.headers.Origin = opts.origin;\n        }\n    }\n    if (parsedUrl.username || parsedUrl.password) {\n        opts.auth = `${parsedUrl.username}:${parsedUrl.password}`;\n    }\n    if (isIpcUrl) {\n        const parts = opts.path.split(\":\");\n        opts.socketPath = parts[0];\n        opts.path = parts[1];\n    }\n    let req;\n    if (opts.followRedirects) {\n        if (websocket._redirects === 0) {\n            websocket._originalIpc = isIpcUrl;\n            websocket._originalSecure = isSecure;\n            websocket._originalHostOrSocketPath = isIpcUrl ? opts.socketPath : parsedUrl.host;\n            const headers = options && options.headers;\n            //\n            // Shallow copy the user provided options so that headers can be changed\n            // without mutating the original object.\n            //\n            options = {\n                ...options,\n                headers: {}\n            };\n            if (headers) {\n                for (const [key, value] of Object.entries(headers)){\n                    options.headers[key.toLowerCase()] = value;\n                }\n            }\n        } else if (websocket.listenerCount(\"redirect\") === 0) {\n            const isSameHost = isIpcUrl ? websocket._originalIpc ? opts.socketPath === websocket._originalHostOrSocketPath : false : websocket._originalIpc ? false : parsedUrl.host === websocket._originalHostOrSocketPath;\n            if (!isSameHost || websocket._originalSecure && !isSecure) {\n                //\n                // Match curl 7.77.0 behavior and drop the following headers. These\n                // headers are also dropped when following a redirect to a subdomain.\n                //\n                delete opts.headers.authorization;\n                delete opts.headers.cookie;\n                if (!isSameHost) delete opts.headers.host;\n                opts.auth = undefined;\n            }\n        }\n        //\n        // Match curl 7.77.0 behavior and make the first `Authorization` header win.\n        // If the `Authorization` header is set, then there is nothing to do as it\n        // will take precedence.\n        //\n        if (opts.auth && !options.headers.authorization) {\n            options.headers.authorization = \"Basic \" + Buffer.from(opts.auth).toString(\"base64\");\n        }\n        req = websocket._req = request(opts);\n        if (websocket._redirects) {\n            //\n            // Unlike what is done for the `'upgrade'` event, no early exit is\n            // triggered here if the user calls `websocket.close()` or\n            // `websocket.terminate()` from a listener of the `'redirect'` event. This\n            // is because the user can also call `request.destroy()` with an error\n            // before calling `websocket.close()` or `websocket.terminate()` and this\n            // would result in an error being emitted on the `request` object with no\n            // `'error'` event listeners attached.\n            //\n            websocket.emit(\"redirect\", websocket.url, req);\n        }\n    } else {\n        req = websocket._req = request(opts);\n    }\n    if (opts.timeout) {\n        req.on(\"timeout\", ()=>{\n            abortHandshake(websocket, req, \"Opening handshake has timed out\");\n        });\n    }\n    req.on(\"error\", (err)=>{\n        if (req === null || req[kAborted]) return;\n        req = websocket._req = null;\n        emitErrorAndClose(websocket, err);\n    });\n    req.on(\"response\", (res)=>{\n        const location = res.headers.location;\n        const statusCode = res.statusCode;\n        if (location && opts.followRedirects && statusCode >= 300 && statusCode < 400) {\n            if (++websocket._redirects > opts.maxRedirects) {\n                abortHandshake(websocket, req, \"Maximum redirects exceeded\");\n                return;\n            }\n            req.abort();\n            let addr;\n            try {\n                addr = new URL(location, address);\n            } catch (e) {\n                const err = new SyntaxError(`Invalid URL: ${location}`);\n                emitErrorAndClose(websocket, err);\n                return;\n            }\n            initAsClient(websocket, addr, protocols, options);\n        } else if (!websocket.emit(\"unexpected-response\", req, res)) {\n            abortHandshake(websocket, req, `Unexpected server response: ${res.statusCode}`);\n        }\n    });\n    req.on(\"upgrade\", (res, socket, head)=>{\n        websocket.emit(\"upgrade\", res);\n        //\n        // The user may have closed the connection from a listener of the\n        // `'upgrade'` event.\n        //\n        if (websocket.readyState !== WebSocket.CONNECTING) return;\n        req = websocket._req = null;\n        const upgrade = res.headers.upgrade;\n        if (upgrade === undefined || upgrade.toLowerCase() !== \"websocket\") {\n            abortHandshake(websocket, socket, \"Invalid Upgrade header\");\n            return;\n        }\n        const digest = createHash(\"sha1\").update(key + GUID).digest(\"base64\");\n        if (res.headers[\"sec-websocket-accept\"] !== digest) {\n            abortHandshake(websocket, socket, \"Invalid Sec-WebSocket-Accept header\");\n            return;\n        }\n        const serverProt = res.headers[\"sec-websocket-protocol\"];\n        let protError;\n        if (serverProt !== undefined) {\n            if (!protocolSet.size) {\n                protError = \"Server sent a subprotocol but none was requested\";\n            } else if (!protocolSet.has(serverProt)) {\n                protError = \"Server sent an invalid subprotocol\";\n            }\n        } else if (protocolSet.size) {\n            protError = \"Server sent no subprotocol\";\n        }\n        if (protError) {\n            abortHandshake(websocket, socket, protError);\n            return;\n        }\n        if (serverProt) websocket._protocol = serverProt;\n        const secWebSocketExtensions = res.headers[\"sec-websocket-extensions\"];\n        if (secWebSocketExtensions !== undefined) {\n            if (!perMessageDeflate) {\n                const message = \"Server sent a Sec-WebSocket-Extensions header but no extension \" + \"was requested\";\n                abortHandshake(websocket, socket, message);\n                return;\n            }\n            let extensions;\n            try {\n                extensions = parse(secWebSocketExtensions);\n            } catch (err) {\n                const message = \"Invalid Sec-WebSocket-Extensions header\";\n                abortHandshake(websocket, socket, message);\n                return;\n            }\n            const extensionNames = Object.keys(extensions);\n            if (extensionNames.length !== 1 || extensionNames[0] !== PerMessageDeflate.extensionName) {\n                const message = \"Server indicated an extension that was not requested\";\n                abortHandshake(websocket, socket, message);\n                return;\n            }\n            try {\n                perMessageDeflate.accept(extensions[PerMessageDeflate.extensionName]);\n            } catch (err) {\n                const message = \"Invalid Sec-WebSocket-Extensions header\";\n                abortHandshake(websocket, socket, message);\n                return;\n            }\n            websocket._extensions[PerMessageDeflate.extensionName] = perMessageDeflate;\n        }\n        websocket.setSocket(socket, head, {\n            allowSynchronousEvents: opts.allowSynchronousEvents,\n            generateMask: opts.generateMask,\n            maxPayload: opts.maxPayload,\n            skipUTF8Validation: opts.skipUTF8Validation\n        });\n    });\n    if (opts.finishRequest) {\n        opts.finishRequest(req, websocket);\n    } else {\n        req.end();\n    }\n}\n/**\n * Emit the `'error'` and `'close'` events.\n *\n * @param {WebSocket} websocket The WebSocket instance\n * @param {Error} The error to emit\n * @private\n */ function emitErrorAndClose(websocket, err) {\n    websocket._readyState = WebSocket.CLOSING;\n    //\n    // The following assignment is practically useless and is done only for\n    // consistency.\n    //\n    websocket._errorEmitted = true;\n    websocket.emit(\"error\", err);\n    websocket.emitClose();\n}\n/**\n * Create a `net.Socket` and initiate a connection.\n *\n * @param {Object} options Connection options\n * @return {net.Socket} The newly created socket used to start the connection\n * @private\n */ function netConnect(options) {\n    options.path = options.socketPath;\n    return net.connect(options);\n}\n/**\n * Create a `tls.TLSSocket` and initiate a connection.\n *\n * @param {Object} options Connection options\n * @return {tls.TLSSocket} The newly created socket used to start the connection\n * @private\n */ function tlsConnect(options) {\n    options.path = undefined;\n    if (!options.servername && options.servername !== \"\") {\n        options.servername = net.isIP(options.host) ? \"\" : options.host;\n    }\n    return tls.connect(options);\n}\n/**\n * Abort the handshake and emit an error.\n *\n * @param {WebSocket} websocket The WebSocket instance\n * @param {(http.ClientRequest|net.Socket|tls.Socket)} stream The request to\n *     abort or the socket to destroy\n * @param {String} message The error message\n * @private\n */ function abortHandshake(websocket, stream, message) {\n    websocket._readyState = WebSocket.CLOSING;\n    const err = new Error(message);\n    Error.captureStackTrace(err, abortHandshake);\n    if (stream.setHeader) {\n        stream[kAborted] = true;\n        stream.abort();\n        if (stream.socket && !stream.socket.destroyed) {\n            //\n            // On Node.js >= 14.3.0 `request.abort()` does not destroy the socket if\n            // called after the request completed. See\n            // https://github.com/websockets/ws/issues/1869.\n            //\n            stream.socket.destroy();\n        }\n        process.nextTick(emitErrorAndClose, websocket, err);\n    } else {\n        stream.destroy(err);\n        stream.once(\"error\", websocket.emit.bind(websocket, \"error\"));\n        stream.once(\"close\", websocket.emitClose.bind(websocket));\n    }\n}\n/**\n * Handle cases where the `ping()`, `pong()`, or `send()` methods are called\n * when the `readyState` attribute is `CLOSING` or `CLOSED`.\n *\n * @param {WebSocket} websocket The WebSocket instance\n * @param {*} [data] The data to send\n * @param {Function} [cb] Callback\n * @private\n */ function sendAfterClose(websocket, data, cb) {\n    if (data) {\n        const length = isBlob(data) ? data.size : toBuffer(data).length;\n        //\n        // The `_bufferedAmount` property is used only when the peer is a client and\n        // the opening handshake fails. Under these circumstances, in fact, the\n        // `setSocket()` method is not called, so the `_socket` and `_sender`\n        // properties are set to `null`.\n        //\n        if (websocket._socket) websocket._sender._bufferedBytes += length;\n        else websocket._bufferedAmount += length;\n    }\n    if (cb) {\n        const err = new Error(`WebSocket is not open: readyState ${websocket.readyState} ` + `(${readyStates[websocket.readyState]})`);\n        process.nextTick(cb, err);\n    }\n}\n/**\n * The listener of the `Receiver` `'conclude'` event.\n *\n * @param {Number} code The status code\n * @param {Buffer} reason The reason for closing\n * @private\n */ function receiverOnConclude(code, reason) {\n    const websocket = this[kWebSocket];\n    websocket._closeFrameReceived = true;\n    websocket._closeMessage = reason;\n    websocket._closeCode = code;\n    if (websocket._socket[kWebSocket] === undefined) return;\n    websocket._socket.removeListener(\"data\", socketOnData);\n    process.nextTick(resume, websocket._socket);\n    if (code === 1005) websocket.close();\n    else websocket.close(code, reason);\n}\n/**\n * The listener of the `Receiver` `'drain'` event.\n *\n * @private\n */ function receiverOnDrain() {\n    const websocket = this[kWebSocket];\n    if (!websocket.isPaused) websocket._socket.resume();\n}\n/**\n * The listener of the `Receiver` `'error'` event.\n *\n * @param {(RangeError|Error)} err The emitted error\n * @private\n */ function receiverOnError(err) {\n    const websocket = this[kWebSocket];\n    if (websocket._socket[kWebSocket] !== undefined) {\n        websocket._socket.removeListener(\"data\", socketOnData);\n        //\n        // On Node.js < 14.0.0 the `'error'` event is emitted synchronously. See\n        // https://github.com/websockets/ws/issues/1940.\n        //\n        process.nextTick(resume, websocket._socket);\n        websocket.close(err[kStatusCode]);\n    }\n    if (!websocket._errorEmitted) {\n        websocket._errorEmitted = true;\n        websocket.emit(\"error\", err);\n    }\n}\n/**\n * The listener of the `Receiver` `'finish'` event.\n *\n * @private\n */ function receiverOnFinish() {\n    this[kWebSocket].emitClose();\n}\n/**\n * The listener of the `Receiver` `'message'` event.\n *\n * @param {Buffer|ArrayBuffer|Buffer[])} data The message\n * @param {Boolean} isBinary Specifies whether the message is binary or not\n * @private\n */ function receiverOnMessage(data, isBinary) {\n    this[kWebSocket].emit(\"message\", data, isBinary);\n}\n/**\n * The listener of the `Receiver` `'ping'` event.\n *\n * @param {Buffer} data The data included in the ping frame\n * @private\n */ function receiverOnPing(data) {\n    const websocket = this[kWebSocket];\n    if (websocket._autoPong) websocket.pong(data, !this._isServer, NOOP);\n    websocket.emit(\"ping\", data);\n}\n/**\n * The listener of the `Receiver` `'pong'` event.\n *\n * @param {Buffer} data The data included in the pong frame\n * @private\n */ function receiverOnPong(data) {\n    this[kWebSocket].emit(\"pong\", data);\n}\n/**\n * Resume a readable stream\n *\n * @param {Readable} stream The readable stream\n * @private\n */ function resume(stream) {\n    stream.resume();\n}\n/**\n * The `Sender` error event handler.\n *\n * @param {Error} The error\n * @private\n */ function senderOnError(err) {\n    const websocket = this[kWebSocket];\n    if (websocket.readyState === WebSocket.CLOSED) return;\n    if (websocket.readyState === WebSocket.OPEN) {\n        websocket._readyState = WebSocket.CLOSING;\n        setCloseTimer(websocket);\n    }\n    //\n    // `socket.end()` is used instead of `socket.destroy()` to allow the other\n    // peer to finish sending queued data. There is no need to set a timer here\n    // because `CLOSING` means that it is already set or not needed.\n    //\n    this._socket.end();\n    if (!websocket._errorEmitted) {\n        websocket._errorEmitted = true;\n        websocket.emit(\"error\", err);\n    }\n}\n/**\n * Set a timer to destroy the underlying raw socket of a WebSocket.\n *\n * @param {WebSocket} websocket The WebSocket instance\n * @private\n */ function setCloseTimer(websocket) {\n    websocket._closeTimer = setTimeout(websocket._socket.destroy.bind(websocket._socket), closeTimeout);\n}\n/**\n * The listener of the socket `'close'` event.\n *\n * @private\n */ function socketOnClose() {\n    const websocket = this[kWebSocket];\n    this.removeListener(\"close\", socketOnClose);\n    this.removeListener(\"data\", socketOnData);\n    this.removeListener(\"end\", socketOnEnd);\n    websocket._readyState = WebSocket.CLOSING;\n    let chunk;\n    //\n    // The close frame might not have been received or the `'end'` event emitted,\n    // for example, if the socket was destroyed due to an error. Ensure that the\n    // `receiver` stream is closed after writing any remaining buffered data to\n    // it. If the readable side of the socket is in flowing mode then there is no\n    // buffered data as everything has been already written and `readable.read()`\n    // will return `null`. If instead, the socket is paused, any possible buffered\n    // data will be read as a single chunk.\n    //\n    if (!this._readableState.endEmitted && !websocket._closeFrameReceived && !websocket._receiver._writableState.errorEmitted && (chunk = websocket._socket.read()) !== null) {\n        websocket._receiver.write(chunk);\n    }\n    websocket._receiver.end();\n    this[kWebSocket] = undefined;\n    clearTimeout(websocket._closeTimer);\n    if (websocket._receiver._writableState.finished || websocket._receiver._writableState.errorEmitted) {\n        websocket.emitClose();\n    } else {\n        websocket._receiver.on(\"error\", receiverOnFinish);\n        websocket._receiver.on(\"finish\", receiverOnFinish);\n    }\n}\n/**\n * The listener of the socket `'data'` event.\n *\n * @param {Buffer} chunk A chunk of data\n * @private\n */ function socketOnData(chunk) {\n    if (!this[kWebSocket]._receiver.write(chunk)) {\n        this.pause();\n    }\n}\n/**\n * The listener of the socket `'end'` event.\n *\n * @private\n */ function socketOnEnd() {\n    const websocket = this[kWebSocket];\n    websocket._readyState = WebSocket.CLOSING;\n    websocket._receiver.end();\n    this.end();\n}\n/**\n * The listener of the socket `'error'` event.\n *\n * @private\n */ function socketOnError() {\n    const websocket = this[kWebSocket];\n    this.removeListener(\"error\", socketOnError);\n    this.on(\"error\", NOOP);\n    if (websocket) {\n        websocket._readyState = WebSocket.CLOSING;\n        this.destroy();\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ws/lib/websocket.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ws/wrapper.mjs":
/*!*************************************!*\
  !*** ./node_modules/ws/wrapper.mjs ***!
  \*************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Receiver: () => (/* reexport default export from named module */ _lib_receiver_js__WEBPACK_IMPORTED_MODULE_1__),\n/* harmony export */   Sender: () => (/* reexport default export from named module */ _lib_sender_js__WEBPACK_IMPORTED_MODULE_2__),\n/* harmony export */   WebSocket: () => (/* reexport default export from named module */ _lib_websocket_js__WEBPACK_IMPORTED_MODULE_3__),\n/* harmony export */   WebSocketServer: () => (/* reexport default export from named module */ _lib_websocket_server_js__WEBPACK_IMPORTED_MODULE_4__),\n/* harmony export */   createWebSocketStream: () => (/* reexport default export from named module */ _lib_stream_js__WEBPACK_IMPORTED_MODULE_0__),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _lib_stream_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./lib/stream.js */ \"(ssr)/./node_modules/ws/lib/stream.js\");\n/* harmony import */ var _lib_receiver_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./lib/receiver.js */ \"(ssr)/./node_modules/ws/lib/receiver.js\");\n/* harmony import */ var _lib_sender_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lib/sender.js */ \"(ssr)/./node_modules/ws/lib/sender.js\");\n/* harmony import */ var _lib_websocket_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./lib/websocket.js */ \"(ssr)/./node_modules/ws/lib/websocket.js\");\n/* harmony import */ var _lib_websocket_server_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./lib/websocket-server.js */ \"(ssr)/./node_modules/ws/lib/websocket-server.js\");\n\n\n\n\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_lib_websocket_js__WEBPACK_IMPORTED_MODULE_3__);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvd3Mvd3JhcHBlci5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFBb0Q7QUFDWDtBQUNKO0FBQ007QUFDYTtBQUV1QjtBQUMvRSxpRUFBZUcsOENBQVNBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9iaGVlbWRpbmUvLi9ub2RlX21vZHVsZXMvd3Mvd3JhcHBlci5tanM/YzVkZSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY3JlYXRlV2ViU29ja2V0U3RyZWFtIGZyb20gJy4vbGliL3N0cmVhbS5qcyc7XG5pbXBvcnQgUmVjZWl2ZXIgZnJvbSAnLi9saWIvcmVjZWl2ZXIuanMnO1xuaW1wb3J0IFNlbmRlciBmcm9tICcuL2xpYi9zZW5kZXIuanMnO1xuaW1wb3J0IFdlYlNvY2tldCBmcm9tICcuL2xpYi93ZWJzb2NrZXQuanMnO1xuaW1wb3J0IFdlYlNvY2tldFNlcnZlciBmcm9tICcuL2xpYi93ZWJzb2NrZXQtc2VydmVyLmpzJztcblxuZXhwb3J0IHsgY3JlYXRlV2ViU29ja2V0U3RyZWFtLCBSZWNlaXZlciwgU2VuZGVyLCBXZWJTb2NrZXQsIFdlYlNvY2tldFNlcnZlciB9O1xuZXhwb3J0IGRlZmF1bHQgV2ViU29ja2V0O1xuIl0sIm5hbWVzIjpbImNyZWF0ZVdlYlNvY2tldFN0cmVhbSIsIlJlY2VpdmVyIiwiU2VuZGVyIiwiV2ViU29ja2V0IiwiV2ViU29ja2V0U2VydmVyIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ws/wrapper.mjs\n");

/***/ })

};
;