/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/menu/page";
exports.ids = ["app/menu/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fmenu%2Fpage&page=%2Fmenu%2Fpage&appPaths=%2Fmenu%2Fpage&pagePath=private-next-app-dir%2Fmenu%2Fpage.tsx&appDir=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fmenu%2Fpage&page=%2Fmenu%2Fpage&appPaths=%2Fmenu%2Fpage&pagePath=private-next-app-dir%2Fmenu%2Fpage.tsx&appDir=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'menu',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/menu/page.tsx */ \"(rsc)/./src/app/menu/page.tsx\")), \"/Users/<USER>/Desktop/BHEEMDINE/src/app/menu/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/Users/<USER>/Desktop/BHEEMDINE/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Desktop/BHEEMDINE/src/app/menu/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/menu/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/menu/page\",\n        pathname: \"/menu\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fmenu%2Fpage&page=%2Fmenu%2Fpage&appPaths=%2Fmenu%2Fpage&pagePath=private-next-app-dir%2Fmenu%2Fpage.tsx&appDir=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fsrc%2Fapp%2Fglobals.css&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fsrc%2Fapp%2Fglobals.css&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fsrc%2Fapp%2Fmenu%2Fpage.tsx&server=true!":
/*!***********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fsrc%2Fapp%2Fmenu%2Fpage.tsx&server=true! ***!
  \***********************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/menu/page.tsx */ \"(ssr)/./src/app/menu/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZzaGl2YSUyRkRlc2t0b3AlMkZCSEVFTURJTkUlMkZzcmMlMkZhcHAlMkZtZW51JTJGcGFnZS50c3gmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYmhlZW1kaW5lLz83NzNkIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL3NoaXZhL0Rlc2t0b3AvQkhFRU1ESU5FL3NyYy9hcHAvbWVudS9wYWdlLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fsrc%2Fapp%2Fmenu%2Fpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/menu/page.tsx":
/*!*******************************!*\
  !*** ./src/app/menu/page.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MenuPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Clock_QrCode_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,QrCode,ShoppingCart,Star!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/qr-code.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_QrCode_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,QrCode,ShoppingCart,Star!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_QrCode_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,QrCode,ShoppingCart,Star!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_QrCode_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,QrCode,ShoppingCart,Star!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction MenuPage() {\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useSearchParams)();\n    const qrCode = searchParams.get(\"qr\") || \"demo\";\n    const roomId = searchParams.get(\"room\");\n    const [cart, setCart] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    // Demo menu data\n    const categories = [\n        {\n            id: \"1\",\n            name: \"Appetizers\",\n            items: [\n                {\n                    id: \"1\",\n                    name: \"Crispy Calamari\",\n                    description: \"Fresh squid rings with spicy marinara sauce\",\n                    price: 12.99,\n                    image: \"/api/placeholder/300/200\",\n                    prepTime: 15,\n                    rating: 4.5,\n                    isVegetarian: false,\n                    allergens: [\n                        \"shellfish\"\n                    ]\n                },\n                {\n                    id: \"2\",\n                    name: \"Bruschetta Trio\",\n                    description: \"Three varieties of our signature bruschetta\",\n                    price: 10.99,\n                    image: \"/api/placeholder/300/200\",\n                    prepTime: 10,\n                    rating: 4.3,\n                    isVegetarian: true,\n                    allergens: [\n                        \"gluten\"\n                    ]\n                }\n            ]\n        },\n        {\n            id: \"2\",\n            name: \"Main Courses\",\n            items: [\n                {\n                    id: \"3\",\n                    name: \"Grilled Salmon\",\n                    description: \"Atlantic salmon with herb butter and seasonal vegetables\",\n                    price: 24.99,\n                    image: \"/api/placeholder/300/200\",\n                    prepTime: 25,\n                    rating: 4.7,\n                    isVegetarian: false,\n                    allergens: [\n                        \"fish\"\n                    ]\n                },\n                {\n                    id: \"4\",\n                    name: \"Mushroom Risotto\",\n                    description: \"Creamy arborio rice with wild mushrooms and parmesan\",\n                    price: 18.99,\n                    image: \"/api/placeholder/300/200\",\n                    prepTime: 20,\n                    rating: 4.4,\n                    isVegetarian: true,\n                    allergens: [\n                        \"dairy\"\n                    ]\n                }\n            ]\n        }\n    ];\n    const addToCart = (item)=>{\n        setCart((prev)=>[\n                ...prev,\n                {\n                    ...item,\n                    id: Date.now()\n                }\n            ]);\n    };\n    const removeFromCart = (itemId)=>{\n        setCart((prev)=>prev.filter((item)=>item.id !== itemId));\n    };\n    const cartTotal = cart.reduce((sum, item)=>sum + item.price, 0);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm border-b border-gray-200 sticky top-0 z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-4 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-10 h-10 bg-orange-600 rounded-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_QrCode_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"w-6 h-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/menu/page.tsx\",\n                                            lineNumber: 92,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/menu/page.tsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-xl font-bold text-gray-900\",\n                                                children: \"TapDine Demo\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/menu/page.tsx\",\n                                                lineNumber: 95,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: [\n                                                    roomId ? `Room ${roomId}` : \"Table Service\",\n                                                    \" • QR: \",\n                                                    qrCode\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/menu/page.tsx\",\n                                                lineNumber: 96,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/menu/page.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/menu/page.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"flex items-center space-x-2 px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors\",\n                                onClick: ()=>{\n                                    if (cart.length > 0) {\n                                        alert(`Order total: $${cartTotal.toFixed(2)}\\nItems: ${cart.length}`);\n                                    }\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_QrCode_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/menu/page.tsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"$\",\n                                            cartTotal.toFixed(2),\n                                            \" (\",\n                                            cart.length,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/menu/page.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/menu/page.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/menu/page.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/menu/page.tsx\",\n                    lineNumber: 88,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/menu/page.tsx\",\n                lineNumber: 87,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 py-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-orange-50 border border-orange-200 rounded-lg p-4 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-lg font-semibold text-orange-900 mb-2\",\n                                children: \"Welcome to Our Digital Menu!\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/menu/page.tsx\",\n                                lineNumber: 121,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-orange-700 text-sm\",\n                                children: \"Browse our delicious offerings and add items to your cart. This is a demo version of the BHEEMDINE platform.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/menu/page.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/menu/page.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 9\n                    }, this),\n                    categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold text-gray-900 mb-6 px-2\",\n                                    children: category.name\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/menu/page.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                    children: category.items.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-48 bg-gray-200 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500 text-sm\",\n                                                        children: \"Menu Item Image\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/menu/page.tsx\",\n                                                        lineNumber: 145,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/menu/page.tsx\",\n                                                    lineNumber: 144,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-start justify-between mb-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"text-lg font-semibold text-gray-900 mb-1\",\n                                                                            children: item.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/menu/page.tsx\",\n                                                                            lineNumber: 152,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-3 text-sm text-gray-600\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center space-x-1\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_QrCode_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                                            className: \"w-4 h-4\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/menu/page.tsx\",\n                                                                                            lineNumber: 157,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            children: [\n                                                                                                item.prepTime,\n                                                                                                \" min\"\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/menu/page.tsx\",\n                                                                                            lineNumber: 158,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/menu/page.tsx\",\n                                                                                    lineNumber: 156,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center space-x-1\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_QrCode_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                                            className: \"w-4 h-4 text-yellow-500\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/menu/page.tsx\",\n                                                                                            lineNumber: 161,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            children: item.rating\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/menu/page.tsx\",\n                                                                                            lineNumber: 162,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/menu/page.tsx\",\n                                                                                    lineNumber: 160,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                item.isVegetarian && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs\",\n                                                                                    children: \"Vegetarian\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/menu/page.tsx\",\n                                                                                    lineNumber: 165,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/menu/page.tsx\",\n                                                                            lineNumber: 155,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/menu/page.tsx\",\n                                                                    lineNumber: 151,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xl font-bold text-orange-600\",\n                                                                    children: [\n                                                                        \"$\",\n                                                                        item.price\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/menu/page.tsx\",\n                                                                    lineNumber: 171,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/menu/page.tsx\",\n                                                            lineNumber: 150,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 text-sm mb-4\",\n                                                            children: item.description\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/menu/page.tsx\",\n                                                            lineNumber: 176,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        item.allergens.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mb-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-500 mb-1\",\n                                                                    children: \"Contains:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/menu/page.tsx\",\n                                                                    lineNumber: 183,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-wrap gap-1\",\n                                                                    children: item.allergens.map((allergen)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"px-2 py-1 bg-red-100 text-red-800 rounded-full text-xs\",\n                                                                            children: allergen\n                                                                        }, allergen, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/menu/page.tsx\",\n                                                                            lineNumber: 186,\n                                                                            columnNumber: 29\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/menu/page.tsx\",\n                                                                    lineNumber: 184,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/menu/page.tsx\",\n                                                            lineNumber: 182,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>addToCart(item),\n                                                            className: \"w-full px-4 py-3 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors font-medium\",\n                                                            children: [\n                                                                \"Add to Cart - $\",\n                                                                item.price\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/menu/page.tsx\",\n                                                            lineNumber: 198,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/menu/page.tsx\",\n                                                    lineNumber: 149,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, item.id, true, {\n                                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/menu/page.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/menu/page.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, category.id, true, {\n                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/menu/page.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 11\n                        }, this)),\n                    cart.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mt-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                children: \"Your Order\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/menu/page.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    cart.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-900\",\n                                                    children: item.name\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/menu/page.tsx\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-900\",\n                                                            children: [\n                                                                \"$\",\n                                                                item.price\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/menu/page.tsx\",\n                                                            lineNumber: 220,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>removeFromCart(item.id),\n                                                            className: \"text-red-600 hover:text-red-700 text-sm\",\n                                                            children: \"Remove\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/menu/page.tsx\",\n                                                            lineNumber: 221,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/menu/page.tsx\",\n                                                    lineNumber: 219,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, item.id, true, {\n                                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/menu/page.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 17\n                                        }, this)),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border-t border-gray-200 pt-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between font-semibold text-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Total\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/menu/page.tsx\",\n                                                    lineNumber: 232,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        \"$\",\n                                                        cartTotal.toFixed(2)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/menu/page.tsx\",\n                                                    lineNumber: 233,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/menu/page.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/menu/page.tsx\",\n                                        lineNumber: 230,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            alert(\"Order submitted! This is a demo - no actual order was placed.\");\n                                            setCart([]);\n                                        },\n                                        className: \"w-full mt-4 px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium\",\n                                        children: \"Place Order\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/menu/page.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/menu/page.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/menu/page.tsx\",\n                        lineNumber: 213,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-blue-50 border border-blue-200 rounded-lg p-4 mt-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-blue-900 font-semibold mb-2\",\n                                children: \"Demo Mode\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/menu/page.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-blue-700 text-sm\",\n                                children: \"This is a demonstration of the BHEEMDINE digital menu system. In a real deployment, orders would be processed and sent to the kitchen in real-time.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/menu/page.tsx\",\n                                lineNumber: 252,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/menu/page.tsx\",\n                        lineNumber: 250,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/menu/page.tsx\",\n                lineNumber: 118,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/menu/page.tsx\",\n        lineNumber: 85,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/menu/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"dfe9ddaa74e5\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYmhlZW1kaW5lLy4vc3JjL2FwcC9nbG9iYWxzLmNzcz80Nzc3Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiZGZlOWRkYWE3NGU1XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"BHEEMDINE - Digital Restaurant Management\",\n    description: \"QR-based digital menu and order management system\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n            children: children\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/layout.tsx\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/layout.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFJTUE7QUFGZ0I7QUFJZixNQUFNQyxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0FBQ2YsRUFBQztBQUVjLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHVDtJQUNDLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztZQUFLQyxXQUFXVCwrSkFBZTtzQkFBR0s7Ozs7Ozs7Ozs7O0FBR3pDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYmhlZW1kaW5lLy4vc3JjL2FwcC9sYXlvdXQudHN4PzU3YTkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gJ25leHQnXG5pbXBvcnQgeyBJbnRlciB9IGZyb20gJ25leHQvZm9udC9nb29nbGUnXG5pbXBvcnQgJy4vZ2xvYmFscy5jc3MnXG5cbmNvbnN0IGludGVyID0gSW50ZXIoeyBzdWJzZXRzOiBbJ2xhdGluJ10gfSlcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6ICdCSEVFTURJTkUgLSBEaWdpdGFsIFJlc3RhdXJhbnQgTWFuYWdlbWVudCcsXG4gIGRlc2NyaXB0aW9uOiAnUVItYmFzZWQgZGlnaXRhbCBtZW51IGFuZCBvcmRlciBtYW5hZ2VtZW50IHN5c3RlbScsXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJlblwiPlxuICAgICAgPGJvZHkgY2xhc3NOYW1lPXtpbnRlci5jbGFzc05hbWV9PntjaGlsZHJlbn08L2JvZHk+XG4gICAgPC9odG1sPlxuICApXG59Il0sIm5hbWVzIjpbImludGVyIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJib2R5IiwiY2xhc3NOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/menu/page.tsx":
/*!*******************************!*\
  !*** ./src/app/menu/page.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Desktop/BHEEMDINE/src/app/menu/page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/lucide-react"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fmenu%2Fpage&page=%2Fmenu%2Fpage&appPaths=%2Fmenu%2Fpage&pagePath=private-next-app-dir%2Fmenu%2Fpage.tsx&appDir=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();