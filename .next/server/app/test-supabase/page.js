/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/test-supabase/page";
exports.ids = ["app/test-supabase/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ftest-supabase%2Fpage&page=%2Ftest-supabase%2Fpage&appPaths=%2Ftest-supabase%2Fpage&pagePath=private-next-app-dir%2Ftest-supabase%2Fpage.tsx&appDir=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ftest-supabase%2Fpage&page=%2Ftest-supabase%2Fpage&appPaths=%2Ftest-supabase%2Fpage&pagePath=private-next-app-dir%2Ftest-supabase%2Fpage.tsx&appDir=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'test-supabase',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/test-supabase/page.tsx */ \"(rsc)/./src/app/test-supabase/page.tsx\")), \"/Users/<USER>/Desktop/BHEEMDINE/src/app/test-supabase/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/Users/<USER>/Desktop/BHEEMDINE/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Desktop/BHEEMDINE/src/app/test-supabase/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/test-supabase/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/test-supabase/page\",\n        pathname: \"/test-supabase\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ftest-supabase%2Fpage&page=%2Ftest-supabase%2Fpage&appPaths=%2Ftest-supabase%2Fpage&pagePath=private-next-app-dir%2Ftest-supabase%2Fpage.tsx&appDir=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZzaGl2YSUyRkRlc2t0b3AlMkZCSEVFTURJTkUlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZhcHAtcm91dGVyLmpzJm1vZHVsZXM9JTJGVXNlcnMlMkZzaGl2YSUyRkRlc2t0b3AlMkZCSEVFTURJTkUlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZlcnJvci1ib3VuZGFyeS5qcyZtb2R1bGVzPSUyRlVzZXJzJTJGc2hpdmElMkZEZXNrdG9wJTJGQkhFRU1ESU5FJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGbGF5b3V0LXJvdXRlci5qcyZtb2R1bGVzPSUyRlVzZXJzJTJGc2hpdmElMkZEZXNrdG9wJTJGQkhFRU1ESU5FJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGbm90LWZvdW5kLWJvdW5kYXJ5LmpzJm1vZHVsZXM9JTJGVXNlcnMlMkZzaGl2YSUyRkRlc2t0b3AlMkZCSEVFTURJTkUlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZyZW5kZXItZnJvbS10ZW1wbGF0ZS1jb250ZXh0LmpzJm1vZHVsZXM9JTJGVXNlcnMlMkZzaGl2YSUyRkRlc2t0b3AlMkZCSEVFTURJTkUlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZzdGF0aWMtZ2VuZXJhdGlvbi1zZWFyY2hwYXJhbXMtYmFpbG91dC1wcm92aWRlci5qcyZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa09BQTBIO0FBQzFILDBPQUE4SDtBQUM5SCx3T0FBNkg7QUFDN0gsa1BBQWtJO0FBQ2xJLHNRQUE0STtBQUM1SSIsInNvdXJjZXMiOlsid2VicGFjazovL2JoZWVtZGluZS8/M2RjMyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9zaGl2YS9EZXNrdG9wL0JIRUVNRElORS9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2FwcC1yb3V0ZXIuanNcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9zaGl2YS9EZXNrdG9wL0JIRUVNRElORS9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2Vycm9yLWJvdW5kYXJ5LmpzXCIpO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvc2hpdmEvRGVza3RvcC9CSEVFTURJTkUvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9sYXlvdXQtcm91dGVyLmpzXCIpO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvc2hpdmEvRGVza3RvcC9CSEVFTURJTkUvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9ub3QtZm91bmQtYm91bmRhcnkuanNcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9zaGl2YS9EZXNrdG9wL0JIRUVNRElORS9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3JlbmRlci1mcm9tLXRlbXBsYXRlLWNvbnRleHQuanNcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9zaGl2YS9EZXNrdG9wL0JIRUVNRElORS9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3N0YXRpYy1nZW5lcmF0aW9uLXNlYXJjaHBhcmFtcy1iYWlsb3V0LXByb3ZpZGVyLmpzXCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fsrc%2Fapp%2Fglobals.css&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fsrc%2Fapp%2Fglobals.css&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fsrc%2Fapp%2Ftest-supabase%2Fpage.tsx&server=true!":
/*!********************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fsrc%2Fapp%2Ftest-supabase%2Fpage.tsx&server=true! ***!
  \********************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/test-supabase/page.tsx */ \"(ssr)/./src/app/test-supabase/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZzaGl2YSUyRkRlc2t0b3AlMkZCSEVFTURJTkUlMkZzcmMlMkZhcHAlMkZ0ZXN0LXN1cGFiYXNlJTJGcGFnZS50c3gmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYmhlZW1kaW5lLz80ZmVmIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL3NoaXZhL0Rlc2t0b3AvQkhFRU1ESU5FL3NyYy9hcHAvdGVzdC1zdXBhYmFzZS9wYWdlLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fsrc%2Fapp%2Ftest-supabase%2Fpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/test-supabase/page.tsx":
/*!****************************************!*\
  !*** ./src/app/test-supabase/page.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TestSupabasePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase_connection_test__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase-connection-test */ \"(ssr)/./src/lib/supabase-connection-test.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n/**\n * Supabase Connection Test Page\n * Simple UI to test and verify Supabase integration\n */ \n\nfunction TestSupabasePage() {\n    const [connectionResult, setConnectionResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [realtimeResult, setRealtimeResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isTestingConnection, setIsTestingConnection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isTestingRealtime, setIsTestingRealtime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleTestConnection = async ()=>{\n        setIsTestingConnection(true);\n        try {\n            const result = await (0,_lib_supabase_connection_test__WEBPACK_IMPORTED_MODULE_2__.testSupabaseConnection)();\n            setConnectionResult(result);\n        } catch (error) {\n            setConnectionResult({\n                success: false,\n                message: \"Test execution failed\",\n                details: {\n                    error: error instanceof Error ? error.message : \"Unknown error\"\n                },\n                timestamp: new Date().toISOString()\n            });\n        } finally{\n            setIsTestingConnection(false);\n        }\n    };\n    const handleTestRealtime = async ()=>{\n        setIsTestingRealtime(true);\n        try {\n            const result = await (0,_lib_supabase_connection_test__WEBPACK_IMPORTED_MODULE_2__.testSupabaseRealtime)();\n            setRealtimeResult(result);\n        } catch (error) {\n            setRealtimeResult({\n                success: false,\n                message: \"Realtime test execution failed\",\n                details: {\n                    error: error instanceof Error ? error.message : \"Unknown error\"\n                },\n                timestamp: new Date().toISOString()\n            });\n        } finally{\n            setIsTestingRealtime(false);\n        }\n    };\n    const TestResultCard = ({ title, result, isLoading })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"border rounded-lg p-6 bg-white shadow-sm\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-semibold mb-4\",\n                    children: title\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/test-supabase/page.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 7\n                }, this),\n                isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2 text-blue-600\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/test-supabase/page.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"Testing...\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/test-supabase/page.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/test-supabase/page.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 9\n                }, this),\n                result && !isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `flex items-center space-x-2 ${result.success ? \"text-green-600\" : \"text-red-600\"}`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xl\",\n                                    children: result.success ? \"✅\" : \"❌\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/test-supabase/page.tsx\",\n                                    lineNumber: 69,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium\",\n                                    children: result.message\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/test-supabase/page.tsx\",\n                                    lineNumber: 70,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/test-supabase/page.tsx\",\n                            lineNumber: 68,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-500\",\n                            children: [\n                                \"Tested at: \",\n                                new Date(result.timestamp).toLocaleString()\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/test-supabase/page.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 11\n                        }, this),\n                        result.details && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-medium text-gray-700 mb-2\",\n                                    children: \"Details:\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/test-supabase/page.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                    className: \"bg-gray-50 p-3 rounded text-xs overflow-auto\",\n                                    children: JSON.stringify(result.details, null, 2)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/test-supabase/page.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/test-supabase/page.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/test-supabase/page.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/test-supabase/page.tsx\",\n            lineNumber: 56,\n            columnNumber: 5\n        }, this);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 py-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-gray-900 mb-2\",\n                            children: \"Supabase Integration Test\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/test-supabase/page.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Verify that your BHEEMDINE application is properly connected to Supabase\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/test-supabase/page.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/test-supabase/page.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid md:grid-cols-2 gap-6 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TestResultCard, {\n                            title: \"Connection Test\",\n                            result: connectionResult,\n                            isLoading: isTestingConnection\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/test-supabase/page.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TestResultCard, {\n                            title: \"Realtime Test\",\n                            result: realtimeResult,\n                            isLoading: isTestingRealtime\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/test-supabase/page.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/test-supabase/page.tsx\",\n                    lineNumber: 102,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleTestConnection,\n                            disabled: isTestingConnection,\n                            className: \"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                            children: isTestingConnection ? \"Testing Connection...\" : \"Test Connection\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/test-supabase/page.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleTestRealtime,\n                            disabled: isTestingRealtime,\n                            className: \"px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                            children: isTestingRealtime ? \"Testing Realtime...\" : \"Test Realtime\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/test-supabase/page.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/test-supabase/page.tsx\",\n                    lineNumber: 116,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-12 bg-white rounded-lg p-6 shadow-sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold mb-4\",\n                            children: \"Environment Configuration\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/test-supabase/page.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2 text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Supabase URL:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/test-supabase/page.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-mono text-xs\",\n                                            children:  true ? `${\"https://cgzcndxnfldupgdddnra.supabase.co\".substring(0, 30)}...` : 0\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/test-supabase/page.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/test-supabase/page.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Anon Key:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/test-supabase/page.tsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-mono text-xs\",\n                                            children:  true ? `${\"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNnemNuZHhuZmxkdXBnZGRkbnJhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI1MjU3OTUsImV4cCI6MjA2ODEwMTc5NX0.XZR5Qlhp31SV2rn30rdhDNKd3x402pLWuma9E-Zy2Oo\".substring(0, 20)}...` : 0\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/test-supabase/page.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/test-supabase/page.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/test-supabase/page.tsx\",\n                            lineNumber: 136,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/test-supabase/page.tsx\",\n                    lineNumber: 134,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: \"/\",\n                        className: \"text-blue-600 hover:text-blue-800 underline\",\n                        children: \"← Back to Home\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/test-supabase/page.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/test-supabase/page.tsx\",\n                    lineNumber: 158,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/test-supabase/page.tsx\",\n            lineNumber: 92,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/test-supabase/page.tsx\",\n        lineNumber: 91,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/test-supabase/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/supabase-connection-test.ts":
/*!*********************************************!*\
  !*** ./src/lib/supabase-connection-test.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   runSupabaseIntegrationTest: () => (/* binding */ runSupabaseIntegrationTest),\n/* harmony export */   testSupabaseConnection: () => (/* binding */ testSupabaseConnection),\n/* harmony export */   testSupabaseRealtime: () => (/* binding */ testSupabaseRealtime)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(ssr)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/**\n * Supabase Connection Test\n * Simple utility to verify Supabase integration is working properly\n */ \n// Test configuration\nconst SUPABASE_URL = \"https://cgzcndxnfldupgdddnra.supabase.co\";\nconst SUPABASE_ANON_KEY = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNnemNuZHhuZmxkdXBnZGRkbnJhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI1MjU3OTUsImV4cCI6MjA2ODEwMTc5NX0.XZR5Qlhp31SV2rn30rdhDNKd3x402pLWuma9E-Zy2Oo\";\n/**\n * Test basic Supabase connection\n */ async function testSupabaseConnection() {\n    const timestamp = new Date().toISOString();\n    try {\n        // Check environment variables\n        if (!SUPABASE_URL || !SUPABASE_ANON_KEY) {\n            return {\n                success: false,\n                message: \"Missing Supabase environment variables\",\n                details: {\n                    hasUrl: !!SUPABASE_URL,\n                    hasAnonKey: !!SUPABASE_ANON_KEY,\n                    urlFormat: SUPABASE_URL ? \"Valid format\" : \"Missing\"\n                },\n                timestamp\n            };\n        }\n        // Create Supabase client\n        const supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(SUPABASE_URL, SUPABASE_ANON_KEY);\n        // Test basic connection by checking auth status\n        const { data: authData, error: authError } = await supabase.auth.getSession();\n        if (authError) {\n            return {\n                success: false,\n                message: \"Failed to connect to Supabase Auth\",\n                details: {\n                    error: authError.message,\n                    code: authError.status\n                },\n                timestamp\n            };\n        }\n        // Test database connection with a simple query\n        const { data: dbData, error: dbError } = await supabase.from(\"information_schema.tables\").select(\"table_name\").limit(1);\n        // Note: This might fail if RLS is enabled and no tables are accessible\n        // That's actually expected behavior for a secure setup\n        return {\n            success: true,\n            message: \"Supabase connection successful\",\n            details: {\n                projectUrl: SUPABASE_URL,\n                authStatus: \"Connected\",\n                sessionExists: !!authData.session,\n                dbQueryTest: dbError ? \"RLS Protected (Expected)\" : \"Accessible\"\n            },\n            timestamp\n        };\n    } catch (error) {\n        return {\n            success: false,\n            message: \"Unexpected error during connection test\",\n            details: {\n                error: error.message,\n                stack: error.stack\n            },\n            timestamp\n        };\n    }\n}\n/**\n * Test Supabase real-time functionality\n */ async function testSupabaseRealtime() {\n    const timestamp = new Date().toISOString();\n    try {\n        if (!SUPABASE_URL || !SUPABASE_ANON_KEY) {\n            return {\n                success: false,\n                message: \"Missing Supabase environment variables for realtime test\",\n                timestamp\n            };\n        }\n        const supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(SUPABASE_URL, SUPABASE_ANON_KEY);\n        // Test realtime connection\n        const channel = supabase.channel(\"connection-test\");\n        return new Promise((resolve)=>{\n            const timeout = setTimeout(()=>{\n                channel.unsubscribe();\n                resolve({\n                    success: false,\n                    message: \"Realtime connection timeout\",\n                    timestamp\n                });\n            }, 5000);\n            channel.on(\"presence\", {\n                event: \"sync\"\n            }, ()=>{\n                clearTimeout(timeout);\n                channel.unsubscribe();\n                resolve({\n                    success: true,\n                    message: \"Realtime connection successful\",\n                    details: {\n                        channelState: \"Connected\",\n                        presenceSync: \"Working\"\n                    },\n                    timestamp\n                });\n            }).subscribe((status)=>{\n                if (status === \"SUBSCRIBED\") {\n                    clearTimeout(timeout);\n                    channel.unsubscribe();\n                    resolve({\n                        success: true,\n                        message: \"Realtime subscription successful\",\n                        details: {\n                            subscriptionStatus: status\n                        },\n                        timestamp\n                    });\n                }\n            });\n        });\n    } catch (error) {\n        return {\n            success: false,\n            message: \"Realtime test failed\",\n            details: {\n                error: error.message\n            },\n            timestamp\n        };\n    }\n}\n/**\n * Run comprehensive Supabase integration test\n */ async function runSupabaseIntegrationTest() {\n    console.log(\"\\uD83D\\uDD0D Running Supabase Integration Test...\\n\");\n    // Test 1: Basic Connection\n    console.log(\"1. Testing basic connection...\");\n    const connectionTest = await testSupabaseConnection();\n    console.log(`   ${connectionTest.success ? \"✅\" : \"❌\"} ${connectionTest.message}`);\n    if (connectionTest.details) {\n        console.log(\"   Details:\", connectionTest.details);\n    }\n    console.log(\"\");\n    // Test 2: Realtime Connection\n    console.log(\"2. Testing realtime connection...\");\n    const realtimeTest = await testSupabaseRealtime();\n    console.log(`   ${realtimeTest.success ? \"✅\" : \"❌\"} ${realtimeTest.message}`);\n    if (realtimeTest.details) {\n        console.log(\"   Details:\", realtimeTest.details);\n    }\n    console.log(\"\");\n    // Summary\n    const allTestsPassed = connectionTest.success && realtimeTest.success;\n    console.log(\"\\uD83D\\uDCCA Test Summary:\");\n    console.log(`   Overall Status: ${allTestsPassed ? \"✅ PASSED\" : \"❌ FAILED\"}`);\n    console.log(`   Connection Test: ${connectionTest.success ? \"PASS\" : \"FAIL\"}`);\n    console.log(`   Realtime Test: ${realtimeTest.success ? \"PASS\" : \"FAIL\"}`);\n    return {\n        success: allTestsPassed,\n        tests: {\n            connection: connectionTest,\n            realtime: realtimeTest\n        }\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/supabase-connection-test.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"dfe9ddaa74e5\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYmhlZW1kaW5lLy4vc3JjL2FwcC9nbG9iYWxzLmNzcz80Nzc3Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiZGZlOWRkYWE3NGU1XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"BHEEMDINE - Digital Restaurant Management\",\n    description: \"QR-based digital menu and order management system\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n            children: children\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/layout.tsx\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/layout.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFJTUE7QUFGZ0I7QUFJZixNQUFNQyxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0FBQ2YsRUFBQztBQUVjLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHVDtJQUNDLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztZQUFLQyxXQUFXVCwrSkFBZTtzQkFBR0s7Ozs7Ozs7Ozs7O0FBR3pDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYmhlZW1kaW5lLy4vc3JjL2FwcC9sYXlvdXQudHN4PzU3YTkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gJ25leHQnXG5pbXBvcnQgeyBJbnRlciB9IGZyb20gJ25leHQvZm9udC9nb29nbGUnXG5pbXBvcnQgJy4vZ2xvYmFscy5jc3MnXG5cbmNvbnN0IGludGVyID0gSW50ZXIoeyBzdWJzZXRzOiBbJ2xhdGluJ10gfSlcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6ICdCSEVFTURJTkUgLSBEaWdpdGFsIFJlc3RhdXJhbnQgTWFuYWdlbWVudCcsXG4gIGRlc2NyaXB0aW9uOiAnUVItYmFzZWQgZGlnaXRhbCBtZW51IGFuZCBvcmRlciBtYW5hZ2VtZW50IHN5c3RlbScsXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJlblwiPlxuICAgICAgPGJvZHkgY2xhc3NOYW1lPXtpbnRlci5jbGFzc05hbWV9PntjaGlsZHJlbn08L2JvZHk+XG4gICAgPC9odG1sPlxuICApXG59Il0sIm5hbWVzIjpbImludGVyIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJib2R5IiwiY2xhc3NOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/test-supabase/page.tsx":
/*!****************************************!*\
  !*** ./src/app/test-supabase/page.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Desktop/BHEEMDINE/src/app/test-supabase/page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@supabase","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/isows","vendor-chunks/tr46","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ftest-supabase%2Fpage&page=%2Ftest-supabase%2Fpage&appPaths=%2Ftest-supabase%2Fpage&pagePath=private-next-app-dir%2Ftest-supabase%2Fpage.tsx&appDir=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();