# BHEEMDINE Environment Variables Example
# Copy this file to .env.local and fill in your actual values

# =====================================================
# APPLICATION CONFIGURATION
# =====================================================

# Application environment (development, staging, production)
NODE_ENV=development

# Application URL (used for QR code generation and API callbacks)
NEXT_PUBLIC_APP_URL=http://localhost:3000

# API Base URL
NEXT_PUBLIC_API_URL=http://localhost:8000/api/v1

# WebSocket URL for real-time features
NEXT_PUBLIC_WS_URL=ws://localhost:8000/ws

# =====================================================
# SUPABASE CONFIGURATION
# =====================================================

# Supabase Project URL
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co

# Supabase Anon Key (Public key for client-side operations)
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key

# Supabase Service Role Key (Secret key for server-side operations)
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key

# Supabase JWT Secret (for verifying JWT tokens)
SUPABASE_JWT_SECRET=your-supabase-jwt-secret

# Database URL for direct connections
DATABASE_URL=postgresql://postgres:password@localhost:5432/bheemdine

# =====================================================
# AUTHENTICATION & SECURITY
# =====================================================

# NextAuth.js configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-nextauth-secret-key-here

# JWT Secret for custom authentication
JWT_SECRET=your-jwt-secret-for-custom-auth

# Session secret for Express sessions
SESSION_SECRET=your-session-secret-key

# Encryption key for sensitive data
ENCRYPTION_KEY=your-32-character-encryption-key

# =====================================================
# THIRD-PARTY INTEGRATIONS
# =====================================================

# Twilio Configuration (for WhatsApp notifications)
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_WHATSAPP_NUMBER=whatsapp:+***********

# SendGrid Configuration (for email notifications)
SENDGRID_API_KEY=your-sendgrid-api-key
SENDGRID_FROM_EMAIL=<EMAIL>

# Stripe Configuration (for payments)
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your-stripe-publishable-key
STRIPE_SECRET_KEY=sk_test_your-stripe-secret-key
STRIPE_WEBHOOK_SECRET=whsec_your-stripe-webhook-secret

# Google Maps API (for location services)
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=your-google-maps-api-key

# Google OAuth Configuration (for authentication)
GOOGLE_CLIENT_ID=your-google-oauth-client-id
GOOGLE_CLIENT_SECRET=your-google-oauth-client-secret

# Admin email whitelist (comma-separated emails that should have admin access)
ADMIN_EMAIL_WHITELIST=<EMAIL>,<EMAIL>

# =====================================================
# FILE STORAGE & CDN
# =====================================================

# AWS S3 Configuration (for file uploads)
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
AWS_S3_BUCKET=bheemdine-uploads

# Cloudinary Configuration (alternative for image management)
CLOUDINARY_CLOUD_NAME=your-cloudinary-cloud-name
CLOUDINARY_API_KEY=your-cloudinary-api-key
CLOUDINARY_API_SECRET=your-cloudinary-api-secret

# =====================================================
# REDIS CONFIGURATION (for caching and sessions)
# =====================================================

# Redis URL for caching and session storage
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=your-redis-password

# =====================================================
# MONITORING & ANALYTICS
# =====================================================

# Sentry Configuration (for error tracking)
NEXT_PUBLIC_SENTRY_DSN=https://<EMAIL>/project-id
SENTRY_AUTH_TOKEN=your-sentry-auth-token

# Google Analytics
NEXT_PUBLIC_GA_ID=GA-XXXXXXXXX

# PostHog Configuration (for product analytics)
NEXT_PUBLIC_POSTHOG_KEY=your-posthog-project-api-key
NEXT_PUBLIC_POSTHOG_HOST=https://app.posthog.com

# =====================================================
# LOGGING & DEBUGGING
# =====================================================

# Log level (error, warn, info, debug)
LOG_LEVEL=info

# Enable debug mode
DEBUG=bheemdine:*

# Enable SQL query logging
LOG_SQL_QUERIES=false

# =====================================================
# QR CODE CONFIGURATION
# =====================================================

# Default QR code base URL
NEXT_PUBLIC_QR_BASE_URL=https://app.tapdine.com/menu

# QR code logo URL (for branding)
NEXT_PUBLIC_QR_LOGO_URL=https://app.tapdine.com/logo.png

# QR code generation settings
QR_CODE_SIZE=256
QR_CODE_ERROR_CORRECTION=M

# =====================================================
# EMAIL CONFIGURATION
# =====================================================

# SMTP Configuration (alternative to SendGrid)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-email-password

# Email templates
EMAIL_FROM_NAME=TapDine
EMAIL_FROM_ADDRESS=<EMAIL>

# =====================================================
# PAYMENT PROCESSING
# =====================================================

# Payment processing fees (in percentage)
PLATFORM_FEE_PERCENTAGE=2.5
STRIPE_FEE_PERCENTAGE=2.9

# Currency settings
DEFAULT_CURRENCY=USD
SUPPORTED_CURRENCIES=USD,EUR,GBP,INR

# =====================================================
# RATE LIMITING & SECURITY
# =====================================================

# Rate limiting (requests per minute)
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_WINDOW_MS=60000

# API request timeout (in milliseconds)
API_TIMEOUT_MS=30000

# Maximum file upload size (in MB)
MAX_FILE_SIZE_MB=10

# CORS allowed origins (comma-separated)
CORS_ALLOWED_ORIGINS=http://localhost:3000,https://tapdine.com

# =====================================================
# FEATURE FLAGS
# =====================================================

# Enable/disable features
FEATURE_REALTIME_ORDERS=true
FEATURE_WHATSAPP_NOTIFICATIONS=true
FEATURE_ANALYTICS_DASHBOARD=true
FEATURE_MULTI_LANGUAGE=false
FEATURE_DARK_MODE=true
FEATURE_BULK_QR_GENERATION=true

# =====================================================
# MULTI-TENANCY CONFIGURATION
# =====================================================

# Default tenant settings
DEFAULT_TENANT_SLUG=demo
DEFAULT_TENANT_NAME=Demo Restaurant

# Tenant isolation level (strict, moderate, relaxed)
TENANT_ISOLATION_LEVEL=strict

# =====================================================
# BACKUP & RECOVERY
# =====================================================

# Database backup configuration
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=bheemdine-backups

# =====================================================
# DEVELOPMENT TOOLS
# =====================================================

# Enable development tools
ENABLE_DEV_TOOLS=true

# Storybook configuration
STORYBOOK_PORT=6006

# API documentation
API_DOCS_ENABLED=true
API_DOCS_PATH=/api/docs

# =====================================================
# PERFORMANCE SETTINGS
# =====================================================

# Database connection pool settings
DB_POOL_MIN=2
DB_POOL_MAX=10

# Cache TTL (in seconds)
CACHE_TTL_SHORT=300
CACHE_TTL_MEDIUM=3600
CACHE_TTL_LONG=86400

# Image optimization
NEXT_IMAGES_QUALITY=75
NEXT_IMAGES_SIZES=640,750,828,1080,1200,1920,2048,3840

# =====================================================
# LOCALIZATION
# =====================================================

# Default language
DEFAULT_LANGUAGE=en

# Supported languages (comma-separated)
SUPPORTED_LANGUAGES=en,es,fr,de,it,pt,hi

# Timezone
DEFAULT_TIMEZONE=UTC

# =====================================================
# WEBHOOK CONFIGURATION
# =====================================================

# Webhook endpoints
WEBHOOK_ORDER_CREATED=https://api.tapdine.com/webhooks/order-created
WEBHOOK_PAYMENT_COMPLETED=https://api.tapdine.com/webhooks/payment-completed

# Webhook secrets
WEBHOOK_SECRET_ORDER=your-order-webhook-secret
WEBHOOK_SECRET_PAYMENT=your-payment-webhook-secret