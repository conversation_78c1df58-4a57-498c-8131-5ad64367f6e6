/**
 * <PERSON><PERSON>t to configure Google OAuth provider in Supabase via API
 * Run this if the dashboard configuration keeps resetting
 */

const { createClient } = require('@supabase/supabase-js')
require('dotenv').config({ path: '.env.local' })

async function configureGoogleOAuth() {
  console.log('🔧 Configuring Google OAuth provider...')

  // Check environment variables
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY
  const googleClientId = process.env.GOOGLE_CLIENT_ID
  const googleClientSecret = process.env.GOOGLE_CLIENT_SECRET

  if (!supabaseUrl || !serviceRoleKey) {
    console.error('❌ Missing Supabase credentials')
    console.log('Required environment variables:')
    console.log('- NEXT_PUBLIC_SUPABASE_URL')
    console.log('- SUPABASE_SERVICE_ROLE_KEY')
    return
  }

  if (!googleClientId || !googleClientSecret) {
    console.error('❌ Missing Google OAuth credentials')
    console.log('Required environment variables:')
    console.log('- GOOGLE_CLIENT_ID')
    console.log('- GOOGLE_CLIENT_SECRET')
    return
  }

  try {
    // Create admin client
    const supabase = createClient(supabaseUrl, serviceRoleKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    })

    console.log('✅ Supabase admin client created')

    // Configure Google OAuth provider
    const { data, error } = await supabase.auth.admin.updateAuthConfig({
      EXTERNAL_GOOGLE_ENABLED: true,
      EXTERNAL_GOOGLE_CLIENT_ID: googleClientId,
      EXTERNAL_GOOGLE_SECRET: googleClientSecret,
      EXTERNAL_GOOGLE_REDIRECT_URI: `${supabaseUrl}/auth/v1/callback`
    })

    if (error) {
      console.error('❌ Failed to configure Google OAuth:', error.message)
      return
    }

    console.log('✅ Google OAuth provider configured successfully')
    console.log('📋 Configuration details:')
    console.log(`   Client ID: ${googleClientId}`)
    console.log(`   Redirect URI: ${supabaseUrl}/auth/v1/callback`)
    console.log('   Status: Enabled')

  } catch (error) {
    console.error('❌ Unexpected error:', error.message)
  }
}

// Run the configuration
configureGoogleOAuth()
  .then(() => {
    console.log('\n🎉 Configuration complete!')
    console.log('💡 You can now test Google OAuth in your application')
    process.exit(0)
  })
  .catch((error) => {
    console.error('\n💥 Configuration failed:', error)
    process.exit(1)
  })
