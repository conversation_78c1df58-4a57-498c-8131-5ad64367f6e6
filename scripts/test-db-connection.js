/**
 * Simple database connection test
 */

const { Client } = require('pg')
require('dotenv').config({ path: '.env.local' })

async function testConnection() {
  console.log('🔍 Testing database connection...')
  
  const databaseUrl = process.env.DATABASE_URL
  console.log('Database URL:', databaseUrl ? `${databaseUrl.substring(0, 50)}...` : 'Not found')
  
  if (!databaseUrl) {
    console.error('❌ DATABASE_URL not found')
    return
  }

  try {
    const client = new Client({
      connectionString: databaseUrl,
      ssl: {
        rejectUnauthorized: false
      }
    })

    console.log('🔌 Connecting to database...')
    await client.connect()
    console.log('✅ Connected successfully!')

    console.log('🔍 Testing query...')
    const result = await client.query('SELECT version()')
    console.log('✅ Query successful:', result.rows[0].version.substring(0, 50) + '...')

    await client.end()
    console.log('✅ Connection test completed successfully!')

  } catch (error) {
    console.error('❌ Connection failed:', error.message)
    
    if (error.message.includes('password')) {
      console.log('💡 Tip: Check your database password in .env.local')
    }
    if (error.message.includes('timeout')) {
      console.log('💡 Tip: Try using the direct connection URL instead of pooler')
    }
  }
}

testConnection()
