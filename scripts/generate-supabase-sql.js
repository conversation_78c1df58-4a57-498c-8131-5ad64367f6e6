/**
 * Generate SQL migration script for Supabase
 * This creates SQL that you can run in the Supabase SQL Editor
 */

const { execSync } = require('child_process')
const fs = require('fs')
const path = require('path')

async function generateSupabaseSQL() {
  console.log('📝 Generating SQL migration script for Supabase...\n')

  try {
    // Create migrations directory if it doesn't exist
    const migrationsDir = path.join(process.cwd(), 'supabase', 'migrations')
    if (!fs.existsSync(migrationsDir)) {
      fs.mkdirSync(migrationsDir, { recursive: true })
      console.log('✅ Created migrations directory')
    }

    // Generate migration SQL
    console.log('1️⃣ Generating migration SQL...')
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').split('T')[0]
    const migrationFile = path.join(migrationsDir, `${timestamp}_initial_schema.sql`)

    // Create a comprehensive SQL migration
    const sqlContent = `
-- BHEEMDINE Database Schema Migration
-- Generated: ${new Date().toISOString()}
-- This script creates the complete database schema for BHEEMDINE

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Create custom types (enums)
DO $$ BEGIN
    CREATE TYPE "RoomStatus" AS ENUM ('AVAILABLE', 'OCCUPIED', 'RESERVED', 'MAINTENANCE');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE "OrderStatus" AS ENUM ('PENDING', 'CONFIRMED', 'PREPARING', 'READY', 'DELIVERED', 'COMPLETED', 'CANCELLED');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE "OrderItemStatus" AS ENUM ('PENDING', 'PREPARING', 'READY', 'SERVED', 'CANCELLED');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE "StaffRole" AS ENUM ('ADMIN', 'MANAGER', 'CHEF', 'WAITER', 'RECEPTIONIST');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE "AllergenSeverity" AS ENUM ('CONTAINS', 'MAY_CONTAIN', 'TRACE');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE "OrderEventType" AS ENUM ('STATUS_CHANGED', 'ASSIGNED', 'UNASSIGNED', 'ITEM_ADDED', 'ITEM_REMOVED', 'ITEM_MODIFIED', 'NOTE_ADDED', 'CANCELLED');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE "TenantStatus" AS ENUM ('ACTIVE', 'SUSPENDED', 'INACTIVE', 'PENDING');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE "PlanType" AS ENUM ('TRIAL', 'BASIC', 'PREMIUM', 'ENTERPRISE');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE "AdminRole" AS ENUM ('OWNER', 'ADMIN', 'MANAGER', 'STAFF', 'READONLY');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE "AdminStatus" AS ENUM ('ACTIVE', 'INACTIVE', 'PENDING', 'SUSPENDED');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Create tables (this is a simplified version - you'll need the full Prisma-generated SQL)
-- Run 'npx prisma db push --preview-feature' to get the complete SQL

-- Enable Row Level Security on all tables
ALTER TABLE IF EXISTS "Tenant" ENABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS "User" ENABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS "AdminUser" ENABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS "Room" ENABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS "MenuItem" ENABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS "Order" ENABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS "OrderItem" ENABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS "Staff" ENABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS "Allergen" ENABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS "MenuItemAllergen" ENABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS "OrderEvent" ENABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS "AuditLog" ENABLE ROW LEVEL SECURITY;

-- Create RLS policies (basic tenant isolation)
-- These policies ensure data isolation between tenants

-- Tenant policies
CREATE POLICY "Tenants can view own data" ON "Tenant"
    FOR ALL USING (id = current_setting('app.current_tenant_id', true)::uuid);

-- User policies  
CREATE POLICY "Users can view own tenant data" ON "User"
    FOR ALL USING ("tenantId" = current_setting('app.current_tenant_id', true)::uuid);

-- Admin User policies
CREATE POLICY "Admin users can view own tenant data" ON "AdminUser"
    FOR ALL USING ("tenantId" = current_setting('app.current_tenant_id', true)::uuid);

-- Room policies
CREATE POLICY "Rooms are tenant-scoped" ON "Room"
    FOR ALL USING ("tenantId" = current_setting('app.current_tenant_id', true)::uuid);

-- MenuItem policies
CREATE POLICY "Menu items are tenant-scoped" ON "MenuItem"
    FOR ALL USING ("tenantId" = current_setting('app.current_tenant_id', true)::uuid);

-- Order policies
CREATE POLICY "Orders are tenant-scoped" ON "Order"
    FOR ALL USING ("tenantId" = current_setting('app.current_tenant_id', true)::uuid);

-- Staff policies
CREATE POLICY "Staff are tenant-scoped" ON "Staff"
    FOR ALL USING ("tenantId" = current_setting('app.current_tenant_id', true)::uuid);

-- Create functions for auth integration
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger AS $$
BEGIN
  INSERT INTO public."AdminUser" (id, "authUserId", email, "tenantId", role, status)
  VALUES (gen_random_uuid(), new.id, new.email, 
          COALESCE(new.raw_user_meta_data->>'tenant_id', '00000000-0000-0000-0000-000000000000')::uuid,
          COALESCE(new.raw_user_meta_data->>'role', 'ADMIN')::"AdminRole",
          'ACTIVE'::"AdminStatus");
  RETURN new;
END;
$$ language plpgsql security definer;

-- Create trigger for new user creation
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE PROCEDURE public.handle_new_user();

-- Create helper functions
CREATE OR REPLACE FUNCTION set_current_tenant(tenant_id uuid)
RETURNS void AS $$
BEGIN
  PERFORM set_config('app.current_tenant_id', tenant_id::text, true);
END;
$$ LANGUAGE plpgsql;

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO anon, authenticated;

-- Insert default data (optional)
-- You can add default tenants, menu items, etc. here

COMMENT ON SCHEMA public IS 'BHEEMDINE Multi-tenant Restaurant Management System';
`;

    fs.writeFileSync(migrationFile, sqlContent)
    console.log(`✅ SQL migration script created: ${migrationFile}`)
    console.log('')

    console.log('📋 Next steps:')
    console.log('1. Run the automatic sync script: npm run sync-db')
    console.log('2. OR manually run this SQL in Supabase SQL Editor')
    console.log('3. Set up Row Level Security policies')
    console.log('4. Configure authentication triggers')

  } catch (error) {
    console.error('❌ Failed to generate SQL:', error.message)
  }
}

generateSupabaseSQL()
