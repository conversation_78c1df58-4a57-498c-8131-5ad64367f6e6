#!/bin/bash

# BHEEMDINE Google OAuth Setup Script
# This script helps set up Google OAuth for the BHEEMDINE application

set -e

echo "🚀 BHEEMDINE Google OAuth Setup"
echo "================================"
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✓${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

print_error() {
    echo -e "${RED}✗${NC} $1"
}

print_info() {
    echo -e "${BLUE}ℹ${NC} $1"
}

# Check if .env.local exists
if [ ! -f ".env.local" ]; then
    print_warning ".env.local not found. Creating from .env.example..."
    cp .env.example .env.local
    print_status "Created .env.local from .env.example"
else
    print_info ".env.local already exists"
fi

echo ""
echo "📋 Required Google OAuth Configuration"
echo "====================================="
echo ""

# Check current environment variables
echo "Current configuration status:"
echo ""

# Function to check if env var is set
check_env_var() {
    local var_name=$1
    local var_value=$(grep "^$var_name=" .env.local 2>/dev/null | cut -d'=' -f2- || echo "")
    
    if [ -z "$var_value" ] || [ "$var_value" = "your-google-oauth-client-id" ] || [ "$var_value" = "your-google-oauth-client-secret" ]; then
        print_error "$var_name: Not configured"
        return 1
    else
        print_status "$var_name: Configured"
        return 0
    fi
}

# Check required variables
GOOGLE_CLIENT_ID_SET=0
GOOGLE_CLIENT_SECRET_SET=0
SUPABASE_URL_SET=0
SUPABASE_ANON_KEY_SET=0

if check_env_var "GOOGLE_CLIENT_ID"; then
    GOOGLE_CLIENT_ID_SET=1
fi

if check_env_var "GOOGLE_CLIENT_SECRET"; then
    GOOGLE_CLIENT_SECRET_SET=1
fi

if check_env_var "NEXT_PUBLIC_SUPABASE_URL"; then
    SUPABASE_URL_SET=1
fi

if check_env_var "NEXT_PUBLIC_SUPABASE_ANON_KEY"; then
    SUPABASE_ANON_KEY_SET=1
fi

echo ""

# If any variables are missing, provide setup instructions
if [ $GOOGLE_CLIENT_ID_SET -eq 0 ] || [ $GOOGLE_CLIENT_SECRET_SET -eq 0 ] || [ $SUPABASE_URL_SET -eq 0 ] || [ $SUPABASE_ANON_KEY_SET -eq 0 ]; then
    echo "🔧 Setup Required"
    echo "================"
    echo ""
    
    if [ $GOOGLE_CLIENT_ID_SET -eq 0 ] || [ $GOOGLE_CLIENT_SECRET_SET -eq 0 ]; then
        echo "Google OAuth Setup:"
        echo "1. Go to https://console.cloud.google.com/"
        echo "2. Create a new project or select existing"
        echo "3. Enable Google+ API"
        echo "4. Create OAuth 2.0 credentials"
        echo "5. Add authorized redirect URIs:"
        echo "   - http://localhost:3000/auth/callback (development)"
        echo "   - https://your-domain.com/auth/callback (production)"
        echo "   - https://your-supabase-project.supabase.co/auth/v1/callback"
        echo ""
    fi
    
    if [ $SUPABASE_URL_SET -eq 0 ] || [ $SUPABASE_ANON_KEY_SET -eq 0 ]; then
        echo "Supabase Setup:"
        echo "1. Go to https://app.supabase.com/"
        echo "2. Create a new project or select existing"
        echo "3. Go to Settings > API"
        echo "4. Copy the Project URL and anon public key"
        echo "5. Go to Authentication > Providers"
        echo "6. Enable Google provider with your OAuth credentials"
        echo ""
    fi
    
    echo "📝 Manual Configuration Required"
    echo "==============================="
    echo ""
    echo "Please edit .env.local and add the following variables:"
    echo ""
    
    if [ $GOOGLE_CLIENT_ID_SET -eq 0 ]; then
        echo "GOOGLE_CLIENT_ID=your-google-oauth-client-id"
    fi
    
    if [ $GOOGLE_CLIENT_SECRET_SET -eq 0 ]; then
        echo "GOOGLE_CLIENT_SECRET=your-google-oauth-client-secret"
    fi
    
    if [ $SUPABASE_URL_SET -eq 0 ]; then
        echo "NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co"
    fi
    
    if [ $SUPABASE_ANON_KEY_SET -eq 0 ]; then
        echo "NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key"
    fi
    
    echo ""
    echo "📖 For detailed instructions, see: docs/google-oauth-setup-guide.md"
    echo ""
    
    read -p "Press Enter after you've updated .env.local..."
    
    # Re-check after user input
    echo ""
    echo "🔄 Re-checking configuration..."
    exec "$0"
else
    print_status "All required environment variables are configured!"
    echo ""
    
    echo "🧪 Testing Configuration"
    echo "======================="
    echo ""
    
    # Test if we can start the development server
    print_info "Starting development server to test configuration..."
    
    if command -v npm &> /dev/null; then
        echo "Run the following command to start the development server:"
        echo ""
        echo "  npm run dev"
        echo ""
        echo "Then navigate to http://localhost:3000/login to test Google OAuth"
    else
        print_warning "npm not found. Please install Node.js and npm first."
    fi
    
    echo ""
    echo "🎉 Setup Complete!"
    echo "================="
    echo ""
    echo "Next steps:"
    echo "1. Start the development server: npm run dev"
    echo "2. Navigate to http://localhost:3000/login"
    echo "3. Test the Google OAuth flow"
    echo "4. Deploy to Vercel with the same environment variables"
    echo ""
    echo "📚 Documentation:"
    echo "- Google OAuth Setup: docs/google-oauth-setup-guide.md"
    echo "- Deployment Guide: DEPLOYMENT.md"
fi
