/**
 * <PERSON><PERSON>t to sync Prisma database schema to Supabase
 * This will create all tables, relationships, and indexes in your Supabase database
 */

const { execSync } = require('child_process')
const fs = require('fs')
require('dotenv').config({ path: '.env.local' })

async function syncDatabaseToSupabase() {
  console.log('🔄 Syncing database schema to Supabase...\n')

  // Check environment variables
  const databaseUrl = process.env.DATABASE_URL
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL

  if (!databaseUrl) {
    console.error('❌ DATABASE_URL not found in environment variables')
    console.log('Make sure your .env.local file has the correct Supabase database URL')
    return
  }

  if (!supabaseUrl) {
    console.error('❌ NEXT_PUBLIC_SUPABASE_URL not found in environment variables')
    return
  }

  console.log('✅ Environment variables found')
  console.log(`📍 Supabase Project: ${supabaseUrl}`)
  console.log(`🗄️  Database URL: ${databaseUrl.substring(0, 50)}...`)
  console.log('')

  try {
    // Step 1: Generate Prisma client
    console.log('1️⃣ Generating Prisma client...')
    execSync('npx prisma generate', { stdio: 'inherit' })
    console.log('✅ Prisma client generated\n')

    // Step 2: Push schema to database
    console.log('2️⃣ Pushing schema to Supabase database...')
    console.log('⚠️  This will create tables and relationships in your Supabase database')
    
    // Use db push for initial schema creation (no migration files needed)
    execSync('npx prisma db push --accept-data-loss', { stdio: 'inherit' })
    console.log('✅ Schema pushed to Supabase successfully\n')

    // Step 3: Verify the sync
    console.log('3️⃣ Verifying database connection...')
    execSync('npx prisma db pull --print', { stdio: 'inherit' })
    console.log('✅ Database connection verified\n')

    console.log('🎉 Database sync completed successfully!')
    console.log('')
    console.log('📋 What was created in your Supabase database:')
    console.log('   ✅ All tables from your Prisma schema')
    console.log('   ✅ Foreign key relationships')
    console.log('   ✅ Indexes for performance')
    console.log('   ✅ Enums and constraints')
    console.log('')
    console.log('🔗 Next steps:')
    console.log('   1. Check your Supabase dashboard → Database → Tables')
    console.log('   2. Set up Row Level Security (RLS) policies')
    console.log('   3. Configure authentication triggers')
    console.log('   4. Test the database connection in your app')

  } catch (error) {
    console.error('❌ Database sync failed:', error.message)
    console.log('')
    console.log('🔧 Troubleshooting tips:')
    console.log('   1. Check your DATABASE_URL is correct')
    console.log('   2. Ensure your Supabase database password is correct')
    console.log('   3. Verify network connectivity to Supabase')
    console.log('   4. Check if your Prisma schema has any syntax errors')
  }
}

// Run the sync
syncDatabaseToSupabase()
  .then(() => {
    console.log('\n✨ Sync process completed!')
    process.exit(0)
  })
  .catch((error) => {
    console.error('\n💥 Sync process failed:', error)
    process.exit(1)
  })
