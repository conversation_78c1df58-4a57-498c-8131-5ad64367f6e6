# 🛠️ BHEEMDINE Test Setup Guide

## 📦 Required Dependencies

### **Install Additional Testing Tools**
```bash
# Component testing
npm install --save-dev @testing-library/react @testing-library/jest-dom @testing-library/user-event

# API mocking
npm install --save-dev msw

# Accessibility testing
npm install --save-dev axe-playwright

# Visual regression testing
npm install --save-dev @storybook/test-runner

# Coverage reporting
npm install --save-dev c8 nyc

# Test utilities
npm install --save-dev faker @types/faker
```

### **Update package.json Scripts**
```json
{
  "scripts": {
    "test": "playwright test",
    "test:e2e": "playwright test tests/e2e",
    "test:api": "playwright test tests/api",
    "test:components": "playwright test tests/components",
    "test:unit": "vitest run",
    "test:watch": "vitest",
    "test:coverage": "vitest run --coverage",
    "test:a11y": "playwright test tests/accessibility",
    "test:performance": "playwright test tests/performance",
    "test:all": "npm run test:unit && npm run test:e2e && npm run test:api",
    "test:ci": "npm run test:all && npm run test:coverage"
  }
}
```

---

## 📁 Test Directory Structure

```
tests/
├── e2e/                          # End-to-end user journey tests
│   ├── customer-journey.spec.ts
│   ├── restaurant-owner-journey.spec.ts
│   └── admin-workflows.spec.ts
├── api/                          # API endpoint tests
│   ├── auth-endpoints.spec.ts
│   ├── menu-endpoints.spec.ts
│   ├── order-endpoints.spec.ts
│   └── tenant-endpoints.spec.ts
├── components/                   # Component integration tests
│   ├── menu-system.spec.ts
│   ├── admin-dashboard.spec.ts
│   ├── authentication.spec.ts
│   └── order-flow.spec.ts
├── unit/                        # Unit tests
│   ├── stores/
│   │   └── menuStore.test.ts
│   ├── utils/
│   │   └── validation.test.ts
│   └── services/
│       └── api-client.test.ts
├── accessibility/               # A11y tests
│   ├── menu-a11y.spec.ts
│   └── admin-a11y.spec.ts
├── performance/                 # Performance tests
│   ├── load-testing.spec.ts
│   └── lighthouse.spec.ts
├── fixtures/                    # Test data
│   ├── menu-data.json
│   ├── order-data.json
│   └── tenant-data.json
├── helpers/                     # Test utilities
│   ├── test-utils.ts
│   ├── mock-data.ts
│   └── page-objects.ts
└── setup/                       # Test configuration
    ├── global-setup.ts
    ├── global-teardown.ts
    └── test-config.ts
```

---

## 🔧 Configuration Files

### **Update playwright.config.ts**
```typescript
import { defineConfig, devices } from '@playwright/test';

export default defineConfig({
  testDir: './tests',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  
  reporter: [
    ['html', { outputFolder: 'test-reports/playwright' }],
    ['json', { outputFile: 'test-reports/results.json' }],
    ['junit', { outputFile: 'test-reports/junit.xml' }],
    ['github']
  ],
  
  use: {
    baseURL: 'http://localhost:3000',
    trace: 'on-first-retry',
    screenshot: 'only-on-failure',
    video: 'retain-on-failure',
    actionTimeout: 10000,
    navigationTimeout: 30000
  },
  
  projects: [
    // E2E Tests
    {
      name: 'e2e-chromium',
      testDir: './tests/e2e',
      use: { ...devices['Desktop Chrome'] },
    },
    {
      name: 'e2e-firefox',
      testDir: './tests/e2e',
      use: { ...devices['Desktop Firefox'] },
    },
    
    // API Tests
    {
      name: 'api-tests',
      testDir: './tests/api',
      use: { ...devices['Desktop Chrome'] },
    },
    
    // Component Tests
    {
      name: 'component-tests',
      testDir: './tests/components',
      use: { ...devices['Desktop Chrome'] },
    },
    
    // Mobile Tests
    {
      name: 'mobile-chrome',
      testDir: './tests/e2e',
      use: { ...devices['Pixel 5'] },
    },
    
    // Accessibility Tests
    {
      name: 'accessibility',
      testDir: './tests/accessibility',
      use: { ...devices['Desktop Chrome'] },
    }
  ],
  
  webServer: {
    command: 'npm run dev',
    url: 'http://localhost:3000',
    reuseExistingServer: !process.env.CI,
    timeout: 120000
  },
  
  globalSetup: require.resolve('./tests/setup/global-setup.ts'),
  globalTeardown: require.resolve('./tests/setup/global-teardown.ts')
});
```

### **Create vitest.config.ts for Unit Tests**
```typescript
import { defineConfig } from 'vitest/config';
import react from '@vitejs/plugin-react';
import path from 'path';

export default defineConfig({
  plugins: [react()],
  test: {
    environment: 'jsdom',
    setupFiles: ['./tests/setup/vitest-setup.ts'],
    coverage: {
      provider: 'c8',
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/',
        'tests/',
        '**/*.d.ts',
        '**/*.config.*',
        '**/coverage/**'
      ]
    }
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src')
    }
  }
});
```

---

## 🧪 Test Utilities and Helpers

### **Test Data Factory**
```typescript
// tests/helpers/mock-data.ts
import { faker } from '@faker-js/faker';

export const createMockTenant = () => ({
  id: faker.string.uuid(),
  name: faker.company.name(),
  slug: faker.internet.domainWord(),
  address: faker.location.streetAddress(),
  city: faker.location.city(),
  state: faker.location.state(),
  zip: faker.location.zipCode(),
  phone: faker.phone.number(),
  email: faker.internet.email(),
  created_at: faker.date.past().toISOString()
});

export const createMockMenuItem = () => ({
  id: faker.string.uuid(),
  name: faker.commerce.productName(),
  description: faker.commerce.productDescription(),
  price: parseFloat(faker.commerce.price()),
  category: faker.helpers.arrayElement(['Appetizers', 'Mains', 'Desserts', 'Beverages']),
  image_url: faker.image.food(),
  available: faker.datatype.boolean(),
  dietary_info: faker.helpers.arrayElements(['vegetarian', 'vegan', 'gluten-free'], 2)
});

export const createMockOrder = () => ({
  id: faker.string.uuid(),
  order_number: `ORD-${faker.number.int({ min: 1000, max: 9999 })}`,
  customer_name: faker.person.fullName(),
  customer_email: faker.internet.email(),
  customer_phone: faker.phone.number(),
  items: Array.from({ length: faker.number.int({ min: 1, max: 5 }) }, createMockMenuItem),
  total_amount: parseFloat(faker.commerce.price()),
  status: faker.helpers.arrayElement(['PENDING', 'CONFIRMED', 'PREPARING', 'READY', 'DELIVERED']),
  created_at: faker.date.recent().toISOString()
});
```

### **Page Object Models**
```typescript
// tests/helpers/page-objects.ts
import { Page, Locator } from '@playwright/test';

export class MenuPage {
  readonly page: Page;
  readonly searchInput: Locator;
  readonly categoryButtons: Locator;
  readonly menuItems: Locator;
  readonly cartButton: Locator;
  readonly cartCount: Locator;

  constructor(page: Page) {
    this.page = page;
    this.searchInput = page.locator('[data-testid="search-input"]');
    this.categoryButtons = page.locator('[data-testid^="category-"]');
    this.menuItems = page.locator('[data-testid^="menu-item-"]');
    this.cartButton = page.locator('[data-testid="cart-summary"]');
    this.cartCount = page.locator('[data-testid="cart-count"]');
  }

  async goto(tenantSlug: string, tableId?: string) {
    const url = tableId 
      ? `/menu?tenant=${tenantSlug}&table=${tableId}`
      : `/menu?tenant=${tenantSlug}`;
    await this.page.goto(url);
  }

  async searchForItem(query: string) {
    await this.searchInput.fill(query);
    await this.page.waitForSelector('[data-testid="search-results"]');
  }

  async selectCategory(category: string) {
    await this.page.click(`[data-testid="category-${category.toLowerCase()}"]`);
  }

  async addItemToCart(itemId: string) {
    await this.page.click(`[data-testid="menu-item-${itemId}"]`);
    await this.page.click('[data-testid="add-to-cart"]');
  }
}

export class AdminDashboard {
  readonly page: Page;
  readonly ordersToday: Locator;
  readonly revenueToday: Locator;
  readonly newOrderNotification: Locator;

  constructor(page: Page) {
    this.page = page;
    this.ordersToday = page.locator('[data-testid="orders-today"]');
    this.revenueToday = page.locator('[data-testid="revenue-today"]');
    this.newOrderNotification = page.locator('[data-testid="new-order-notification"]');
  }

  async goto() {
    await this.page.goto('/admin/dashboard');
  }

  async viewOrder(orderId: string) {
    await this.page.click(`[data-testid="order-${orderId}"]`);
  }

  async updateOrderStatus(orderId: string, status: string) {
    await this.page.selectOption(`[data-testid="order-${orderId}-status"]`, status);
    await this.page.click(`[data-testid="update-order-${orderId}"]`);
  }
}
```

### **Test Setup Files**
```typescript
// tests/setup/global-setup.ts
import { chromium, FullConfig } from '@playwright/test';

async function globalSetup(config: FullConfig) {
  // Setup test database
  // Create test tenant data
  // Initialize mock services
  console.log('Setting up test environment...');
}

export default globalSetup;
```

```typescript
// tests/setup/vitest-setup.ts
import '@testing-library/jest-dom';
import { vi } from 'vitest';

// Mock Next.js router
vi.mock('next/router', () => ({
  useRouter: () => ({
    push: vi.fn(),
    pathname: '/',
    query: {},
    asPath: '/'
  })
}));

// Mock Zustand stores
vi.mock('@/stores/menuStore', () => ({
  useMenuStore: () => ({
    menuItems: [],
    cartItems: [],
    addToCart: vi.fn(),
    removeFromCart: vi.fn()
  })
}));
```

---

## 🚀 Quick Start Commands

### **Run All Tests**
```bash
# Run complete test suite
npm run test:all

# Run specific test categories
npm run test:e2e
npm run test:api
npm run test:components

# Run tests with coverage
npm run test:coverage

# Run tests in watch mode (development)
npm run test:watch
```

### **Debug Tests**
```bash
# Run tests in headed mode
npx playwright test --headed

# Run specific test file
npx playwright test tests/e2e/customer-journey.spec.ts

# Debug specific test
npx playwright test --debug tests/e2e/customer-journey.spec.ts
```

This setup provides a comprehensive testing foundation that can scale with the application's growth and complexity.
