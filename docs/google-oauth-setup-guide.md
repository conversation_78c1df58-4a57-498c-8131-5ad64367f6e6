# Google OAuth Setup Guide for BHEEMDINE

This guide will walk you through setting up Google OAuth authentication for the BHEEMDINE application.

## Prerequisites

- Google Cloud Console account
- Supabase project
- BHEEMDINE application deployed or running locally

## Step 1: Create Google OAuth Credentials

### 1.1 Access Google Cloud Console

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Sign in with your Google account
3. Create a new project or select an existing one

### 1.2 Enable Google+ API

1. Navigate to **APIs & Services** > **Library**
2. Search for "Google+ API" or "Google Identity"
3. Click on "Google+ API" and click **Enable**

### 1.3 Create OAuth 2.0 Credentials

1. Go to **APIs & Services** > **Credentials**
2. Click **+ CREATE CREDENTIALS** > **OAuth client ID**
3. If prompted, configure the OAuth consent screen first:
   - Choose **External** user type
   - Fill in the required fields:
     - App name: "BHEEMDINE"
     - User support email: your email
     - Developer contact information: your email
   - Add scopes: `email`, `profile`, `openid`
   - Add test users if needed

4. For OAuth client ID:
   - Application type: **Web application**
   - Name: "BHEEMDINE Web Client"
   - Authorized JavaScript origins:
     - `http://localhost:3000` (for development)
     - `https://your-domain.com` (for production)
   - Authorized redirect URIs:
     - `http://localhost:3000/auth/callback` (for development)
     - `https://your-domain.com/auth/callback` (for production)
     - `https://your-supabase-project.supabase.co/auth/v1/callback` (Supabase callback)

5. Click **Create**
6. Copy the **Client ID** and **Client Secret**

## Step 2: Configure Supabase

### 2.1 Add Google OAuth Provider

1. Go to your [Supabase Dashboard](https://app.supabase.com/)
2. Select your project
3. Navigate to **Authentication** > **Providers**
4. Find **Google** and click to configure
5. Enable the Google provider
6. Enter your Google OAuth credentials:
   - **Client ID**: The Client ID from Google Cloud Console
   - **Client Secret**: The Client Secret from Google Cloud Console
7. Click **Save**

### 2.2 Configure Redirect URLs

1. In Supabase, go to **Authentication** > **URL Configuration**
2. Add your redirect URLs:
   - Site URL: `https://your-domain.com` (production) or `http://localhost:3000` (development)
   - Redirect URLs:
     - `http://localhost:3000/auth/callback`
     - `https://your-domain.com/auth/callback`

## Step 3: Update Environment Variables

### 3.1 Local Development (.env.local)

Create or update your `.env.local` file:

```bash
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key

# Google OAuth Configuration
GOOGLE_CLIENT_ID=your-google-oauth-client-id
GOOGLE_CLIENT_SECRET=your-google-oauth-client-secret

# Admin email whitelist
ADMIN_EMAIL_WHITELIST=<EMAIL>,<EMAIL>

# Application URLs
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

### 3.2 Production Environment (Vercel)

Set these environment variables in your Vercel dashboard:

1. Go to your Vercel project dashboard
2. Navigate to **Settings** > **Environment Variables**
3. Add the following variables:

```
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key
GOOGLE_CLIENT_ID=your-google-oauth-client-id
GOOGLE_CLIENT_SECRET=your-google-oauth-client-secret
ADMIN_EMAIL_WHITELIST=<EMAIL>,<EMAIL>
NEXT_PUBLIC_APP_URL=https://your-domain.com
```

## Step 4: Test the Integration

### 4.1 Local Testing

1. Start your development server:
   ```bash
   npm run dev
   ```

2. Navigate to `http://localhost:3000/login`
3. Click "Continue with Google"
4. Complete the OAuth flow
5. Verify you're redirected back to the application

### 4.2 Production Testing

1. Deploy your application to Vercel
2. Navigate to your production URL
3. Test the Google OAuth flow
4. Verify the callback works correctly

## Step 5: Database Setup

The OAuth callback will automatically create user profiles. Ensure your database has the required tables:

### 5.1 AdminUsers Table

```sql
-- This should already exist from your Prisma schema
-- Verify the table has these columns:
-- - auth_user_id (references Supabase auth.users.id)
-- - email
-- - first_name
-- - last_name
-- - avatar_url
-- - role
-- - status
```

### 5.2 Users Table (for customers)

```sql
-- This should already exist from your Prisma schema
-- Verify the table has these columns:
-- - auth_user_id (references Supabase auth.users.id)
-- - email
-- - name
-- - avatar_url
```

## Troubleshooting

### Common Issues

1. **"redirect_uri_mismatch" error**
   - Verify the redirect URI in Google Cloud Console matches exactly
   - Check for trailing slashes or protocol mismatches

2. **"invalid_client" error**
   - Verify the Client ID and Client Secret are correct
   - Check that the Google+ API is enabled

3. **User not created in database**
   - Check the OAuth callback logs
   - Verify the database tables exist
   - Check the admin email whitelist configuration

4. **Infinite redirect loop**
   - Verify the callback route is working
   - Check for conflicting authentication middleware

### Debug Steps

1. Check browser developer tools for console errors
2. Verify environment variables are set correctly
3. Check Supabase logs in the dashboard
4. Test with a simple OAuth flow first

## Security Considerations

1. **Environment Variables**: Never commit OAuth secrets to version control
2. **Redirect URIs**: Only add trusted domains to the authorized redirect URIs
3. **Admin Access**: Carefully manage the admin email whitelist
4. **HTTPS**: Always use HTTPS in production for OAuth flows

## Next Steps

After setting up Google OAuth:

1. Test the complete authentication flow
2. Customize the user profile creation logic
3. Set up proper role-based access control
4. Configure additional OAuth providers if needed
5. Set up monitoring and logging for authentication events

## Support

If you encounter issues:

1. Check the Supabase documentation: https://supabase.com/docs/guides/auth/social-login/auth-google
2. Review Google OAuth documentation: https://developers.google.com/identity/protocols/oauth2
3. Check the application logs for detailed error messages
